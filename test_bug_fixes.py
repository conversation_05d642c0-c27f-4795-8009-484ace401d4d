#!/usr/bin/env python3
"""
Comprehensive test suite for all bug fixes in the car racing game
"""

import json
import sys
import os
import tempfile
import shutil

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def backup_data():
    """Create backup of data files"""
    backup_dir = tempfile.mkdtemp(prefix="game_backup_")
    for file in ['profile.json', 'garage.json', 'shop_data.json']:
        src = f'data/{file}'
        if os.path.exists(src):
            shutil.copy2(src, backup_dir)
    return backup_dir

def restore_data(backup_dir):
    """Restore data files from backup"""
    for file in ['profile.json', 'garage.json', 'shop_data.json']:
        backup_file = os.path.join(backup_dir, file)
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, f'data/{file}')
    shutil.rmtree(backup_dir)

def test_shop_money_deduction():
    """Test that money is not deducted twice in shop purchases"""
    print("=== Testing Shop Money Deduction Fix ===")
    
    try:
        # Load current profile
        with open('data/profile.json', 'r') as f:
            profile_before = json.load(f)
        
        initial_money = profile_before['money']
        print(f"Initial money: {initial_money}")
        
        # Test save_purchase function directly
        from shop import save_purchase
        
        # Create a test item
        test_item = {
            "name": "Test Item",
            "value": 100,
            "category": "engine",
            "horsepower": 150,
            "weight": 200
        }
        
        # Test purchase
        try:
            save_purchase(test_item, "part")
            
            # Check money after purchase
            with open('data/profile.json', 'r') as f:
                profile_after = json.load(f)
            
            money_after = profile_after['money']
            money_deducted = initial_money - money_after
            
            print(f"Money after purchase: {money_after}")
            print(f"Money deducted: {money_deducted}")
            
            if money_deducted == test_item['value']:
                print("✅ Money deduction working correctly")
                return True
            else:
                print(f"❌ Wrong money deduction: expected {test_item['value']}, got {money_deducted}")
                return False
                
        except Exception as e:
            print(f"❌ Purchase failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_garage_error_handling():
    """Test garage error handling and validation"""
    print("\n=== Testing Garage Error Handling ===")
    
    try:
        from garage_screen import save_car_parts, fix_car_data_consistency
        
        # Test invalid car index
        result = save_car_parts(-1, {})
        if not result:
            print("✅ Invalid car index properly rejected")
        else:
            print("❌ Invalid car index not rejected")
            return False
        
        # Test data consistency fix
        consistency_fixed = fix_car_data_consistency()
        print(f"✅ Data consistency check completed (fixed: {consistency_fixed})")
        
        return True
        
    except Exception as e:
        print(f"❌ Garage test failed: {e}")
        return False

def test_save_system_integrity():
    """Test save system data integrity"""
    print("\n=== Testing Save System Integrity ===")
    
    try:
        from save_system import save_system
        
        # Test save creation
        result = save_system.save_game(1)
        if result:
            print("✅ Save game successful")
        else:
            print("❌ Save game failed")
            return False
        
        # Test save loading
        result = save_system.load_game(1)
        if result:
            print("✅ Load game successful")
        else:
            print("❌ Load game failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Save system test failed: {e}")
        return False

def test_data_validation():
    """Test data validation and error handling"""
    print("\n=== Testing Data Validation ===")
    
    try:
        # Test profile data structure
        with open('data/profile.json', 'r') as f:
            profile_data = json.load(f)
        
        required_fields = ['username', 'money', 'level', 'cars', 'inventory']
        for field in required_fields:
            if field not in profile_data:
                print(f"❌ Missing required field: {field}")
                return False
        
        print("✅ Profile data structure valid")
        
        # Test garage data structure
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        if not isinstance(garage_data, list):
            print("❌ Garage data should be a list")
            return False
        
        print("✅ Garage data structure valid")
        
        return True
        
    except Exception as e:
        print(f"❌ Data validation test failed: {e}")
        return False

def test_error_recovery():
    """Test error recovery mechanisms"""
    print("\n=== Testing Error Recovery ===")
    
    try:
        # Test handling of missing files
        from shop import save_purchase
        
        # Backup original file
        if os.path.exists('data/profile.json'):
            shutil.copy2('data/profile.json', 'data/profile.json.backup')
        
        # Remove profile file temporarily
        if os.path.exists('data/profile.json'):
            os.remove('data/profile.json')
        
        # Test error handling
        test_item = {"name": "Test", "value": 100, "category": "engine"}
        try:
            save_purchase(test_item, "part")
            print("❌ Should have failed with missing file")
            return False
        except ValueError as e:
            if "Profile file not found" in str(e):
                print("✅ Missing file error handled correctly")
            else:
                print(f"❌ Wrong error message: {e}")
                return False
        
        # Restore file
        if os.path.exists('data/profile.json.backup'):
            shutil.move('data/profile.json.backup', 'data/profile.json')
        
        return True
        
    except Exception as e:
        print(f"❌ Error recovery test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 Comprehensive Bug Fix Test Suite")
    print("=" * 60)
    
    # Create backup
    backup_dir = backup_data()
    
    try:
        tests = [
            test_shop_money_deduction,
            test_garage_error_handling,
            test_save_system_integrity,
            test_data_validation,
            test_error_recovery
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        print("\n" + "=" * 60)
        print(f"🔧 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! All bugs have been fixed.")
        else:
            print(f"⚠️  {total - passed} tests failed. Some bugs may remain.")
        
        return passed == total
        
    finally:
        # Restore backup
        restore_data(backup_dir)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
