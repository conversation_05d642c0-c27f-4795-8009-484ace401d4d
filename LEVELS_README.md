# 🏁 System Poziomów - Xtreme Cars

## 📋 Przegląd

Rozbudowany system poziomów z 10 progresywnymi przeciwnikami, każdy z unikalną konfiguracją i rosnącą trudnością. System oferuje wyzwanie dla graczy na każdym etapie rozwoju.

## 🎯 **Nowe Funkcje**

### ✅ **10 Poziomów Przeciwników**
- **Progresywna trudność** - każdy poziom jest trudniejszy od poprzedniego
- **Unikalne nazwy** - każdy przeciwnik ma charakterystyczną nazwę
- **Zróżnicowane konfiguracje** - różne silniki, turbo, intercoolery i ECU
- **Optymalizacja wagi** - wyższe poziomy mają lżejsze samochody

### ✅ **System Poziomów Wyścigów**
- **Niezależny od poziomu gracza** - oddzielny postęp w wyścigach
- **Automatyczny awans** - wygrana = przejście na następny poziom
- **Wyświetlanie w profilu** - osobne informacje o poziomie gracza i wyścigów
- **Zabezpieczenia** - obsługa poziomów powyżej dostępnych

### ✅ **Ekran Informacji o Poziomie**
- **Podgląd przeciwnika** przed wyścigiem
- **Statystyki przeciwnika** - moc, waga, stosunek mocy do wagi
- **Lista części** - szczegółowe informacje o wyposażeniu
- **Wskaźnik trudności** - wizualna reprezentacja poziomu

### ✅ **Ekran Ukończenia Poziomu**
- **Podsumowanie wyników** po wygranej
- **Informacje o nagrodach** - pieniądze i doświadczenie
- **Awanse poziomów** - powiadomienia o postępie
- **Automatyczne przejście** - opcja szybkiego kontynuowania

## 🏆 **Lista Poziomów**

| Poziom | Przeciwnik | Moc (HP) | Waga (kg) | Stosunek P/W |
|--------|------------|----------|-----------|--------------|
| 1 | Początkujący Kierowca | 132 | 682 | 0.19 |
| 2 | Amator Tuningu | 228 | 727 | 0.31 |
| 3 | Lokalny Mistrz | 378 | 757 | 0.50 |
| 4 | Uliczny Zawodnik | 700 | 772 | 0.91 |
| 5 | Profesjonalny Tuner | 936 | 771 | 1.21 |
| 6 | Mistrz Drag Race | 1368 | 771 | 1.77 |
| 7 | Legenda Ulicy | 1800 | 818 | 2.20 |
| 8 | Król Asfaltu | 2186 | 821 | 2.66 |
| 9 | Niepokonany Mistrz | 2186 | 801 | 2.73 |
| 10 | Ostateczny Boss | 2186 | 781 | 2.80 |

## 🔧 **Konfiguracje Przeciwników**

### **Poziomy 1-2: Początkujący**
- **Silniki**: Basic I4, Tuned I4
- **Turbo**: Brak lub Small Turbo
- **Intercooler**: Brak lub Basic Front Mount
- **ECU**: Stage 1-2 Tune

### **Poziomy 3-4: Średniozaawansowani**
- **Silniki**: V6 Naturally Aspirated, V6 Twin Turbo
- **Turbo**: Medium/Large Turbo
- **Intercooler**: Large Front Mount, Water-to-Air
- **ECU**: Stage 3, Custom Dyno Tune

### **Poziomy 5-7: Zaawansowani**
- **Silniki**: V8 Muscle, V8 High Performance, V10 Supercar
- **Turbo**: Ball Bearing, Variable Geometry, Twin Turbo
- **Intercooler**: Race Spec Intercooler
- **ECU**: Race ECU, Pro Tuner ECU

### **Poziomy 8-10: Eksperci**
- **Silniki**: V12 Hypercar
- **Turbo**: Twin Turbo Setup
- **Intercooler**: Race Spec Intercooler
- **ECU**: Pro Tuner ECU
- **Optymalizacja**: Progresywnie lżejsze samochody

## 🎮 **Mechanika Rozgrywki**

### **Postęp w Poziomach**
1. **Wygrana** = awans na następny poziom wyścigów
2. **Przegrana** = pozostanie na tym samym poziomie
3. **Nagrody** = większe za wygraną, mniejsze za przegraną
4. **Doświadczenie** = więcej XP za wygraną

### **System Nagród**
- **Wygrana**: `500 * poziom / czas_gracza` $
- **Przegrana**: `poziom` $
- **XP za wygraną**: `nagroda * (3 + poziom_gracza / 10)`
- **XP za przegraną**: `nagroda * (1 + poziom_gracza / 20)`

### **Wyświetlanie Informacji**
- **Przed wyścigiem**: Ekran informacji o przeciwniku
- **Podczas wyścigu**: Nazwa przeciwnika i prędkość
- **Po wyścigu**: Ekran ukończenia poziomu (tylko przy wygranej)

## 🛠 **Implementacja Techniczna**

### **Pliki Systemu**
- **`data/oponent_levels.json`** - definicje wszystkich przeciwników
- **`level_info.py`** - ekrany informacji i ukończenia poziomu
- **`game.py`** - logika wyboru przeciwnika i postępu
- **`cars.py`** - wyświetlanie nazw przeciwników

### **Struktura Danych Przeciwnika**
```json
{
  "level": 1,
  "name": "classic",
  "opponent_name": "Początkujący Kierowca",
  "color": {"0": "red", "1": "violet"},
  "weight": 500,
  "parts": {
    "engine": {...},
    "turbo": {...},
    "intercooler": {...},
    "ecu": {...}
  }
}
```

### **Logika Wyboru Poziomu**
```python
# Użyj race_level dla wyboru przeciwnika
current_level = user_data.get('race_level', user_data["level"]['current'])

# Zabezpieczenie przed przekroczeniem dostępnych poziomów
max_level = len(opponent_data)
if current_level > max_level:
    current_level = max_level

opponent = opponent_data[current_level - 1]
```

## 📊 **Krzywa Trudności**

### **Progresja Mocy**
- **Poziom 1**: 132 HP (stosunek P/W: 0.19)
- **Poziom 5**: 936 HP (stosunek P/W: 1.21) - **6x wzrost**
- **Poziom 10**: 2186 HP (stosunek P/W: 2.80) - **15x wzrost**

### **Optymalizacja Wagi**
- **Wczesne poziomy**: Cięższe samochody (680-770 kg)
- **Późne poziomy**: Lżejsze samochody (780-820 kg)
- **Ostatnie poziomy**: Progresywna redukcja wagi

### **Wzrost Trudności**
- **100% poziomów** ma wyższą trudność niż poprzedni
- **Płynna progresja** bez nagłych skoków trudności
- **Zrównoważone wyzwanie** dla wszystkich etapów gry

## 🎯 **Strategia dla Gracza**

### **Poziomy 1-3: Nauka Podstaw**
- **Cel**: Opanowanie sterowania i podstawowego tuningu
- **Wymagania**: Basic I4 + podstawowe części
- **Nagrody**: 500-1500 $ za wygraną

### **Poziomy 4-6: Rozwój Średni**
- **Cel**: Inwestycja w lepsze silniki i turbo
- **Wymagania**: V6 + Medium/Large Turbo
- **Nagrody**: 2000-3000 $ za wygraną

### **Poziomy 7-10: Mistrzostwo**
- **Cel**: Maksymalna optymalizacja i najlepsze części
- **Wymagania**: V8/V10/V12 + najlepsze części
- **Nagrody**: 3500-5000 $ za wygraną

## 🚀 **Przyszłe Rozszerzenia**

### **Planowane Funkcje**
- **Więcej poziomów** (11-20) dla zaawansowanych graczy
- **Różne typy wyścigów** - drag, circuit, drift
- **Sezonowe wydarzenia** - specjalne przeciwnicy
- **Rankingi** - porównanie z innymi graczami

### **Możliwe Ulepszenia**
- **Adaptacyjna trudność** - dostosowanie do umiejętności gracza
- **Losowe modyfikacje** - różne konfiguracje tego samego poziomu
- **Narracja** - historie przeciwników i ich motywacje
- **Osiągnięcia** - specjalne nagrody za pokonanie poziomów

## 🎉 **Podsumowanie**

System poziomów znacznie wzbogaca rozgrywkę poprzez:

✅ **10 unikalnych przeciwników** z progresywną trudnością  
✅ **Zrównoważoną krzywą trudności** - 15x wzrost mocy  
✅ **Przejrzyste informacje** - ekrany przed i po wyścigu  
✅ **Niezależny postęp** - oddzielne poziomy gracza i wyścigów  
✅ **Strategiczną głębię** - wymagane planowanie rozwoju  
✅ **Długoterminową motywację** - jasne cele do osiągnięcia  

System jest w pełni funkcjonalny i oferuje godziny wciągającej rozgrywki! 🏁🏆
