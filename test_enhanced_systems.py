#!/usr/bin/env python3
"""
Test script for enhanced car racing game systems
Tests all new features: part selling, enhanced rewards, performance calculations,
fuel consumption, tire wear, maintenance, and enhanced depreciation.
"""

import json
import time
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from valuation_system import valuation_system
from fuel_system import fuel_system
from tire_system import tire_system
from maintenance_system import maintenance_system

def test_valuation_system():
    """Test the enhanced valuation system"""
    print("=== Testing Valuation System ===")
    
    # Load test car data
    try:
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        if not garage_data:
            print("❌ No car data found")
            return False
        
        car_data = garage_data[0]  # Test with first car
        
        # Test basic valuation
        valuation = valuation_system.calculate_car_value(car_data)
        print(f"✅ Basic car valuation: {valuation['total_value']}$")
        
        # Test enhanced performance calculation
        performance = valuation_system.calculate_enhanced_performance(car_data)
        print(f"✅ Performance class: {performance['performance_class']}")
        print(f"✅ Total HP: {performance['total_horsepower']}")
        print(f"✅ Power-to-weight: {performance['power_to_weight_ratio']}")
        
        # Test with aged usage data
        aged_usage = {
            "car_age_days": 365,  # 1 year old
            "races_completed": 100,
            "engine_age_days": 365,
            "turbo_age_days": 365,
            "intercooler_age_days": 365,
            "ecu_age_days": 365,
            "last_update": int(time.time())
        }
        
        aged_valuation = valuation_system.calculate_car_value(car_data, aged_usage)
        print(f"✅ Aged car valuation: {aged_valuation['total_value']}$ (condition: {int(aged_valuation['car_condition']*100)}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Valuation system test failed: {e}")
        return False

def test_fuel_system():
    """Test the fuel consumption system"""
    print("\n=== Testing Fuel System ===")
    
    try:
        # Initialize fuel data for car 0
        fuel_system.initialize_fuel_data(0)
        
        # Get fuel data
        fuel_data = fuel_system.get_fuel_data(0)
        print(f"✅ Initial fuel: {fuel_data['current_fuel']}L / {fuel_data['max_capacity']}L")
        
        # Load car data for consumption calculation
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        car_data = garage_data[0]
        
        # Test fuel consumption
        consumption_rate = fuel_system.calculate_fuel_consumption_rate(car_data)
        print(f"✅ Fuel consumption rate: {consumption_rate:.2f}L/s")
        
        # Simulate race fuel consumption
        race_time = 30  # 30 seconds race
        fuel_result = fuel_system.consume_fuel(0, race_time, car_data)
        print(f"✅ Fuel consumed in {race_time}s race: {fuel_result['fuel_consumed']:.2f}L")
        print(f"✅ Remaining fuel: {fuel_result['remaining_fuel']:.2f}L ({int(fuel_result['fuel_percentage']*100)}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Fuel system test failed: {e}")
        return False

def test_tire_system():
    """Test the tire wear system"""
    print("\n=== Testing Tire System ===")
    
    try:
        # Initialize tire data for car 0
        tire_system.initialize_tire_data(0)
        
        # Get tire info
        tire_info = tire_system.get_tire_info(0)
        print(f"✅ Initial tires: {tire_info['type_name']}")
        print(f"✅ Tire condition: {tire_info['condition']:.1f}% ({tire_info['condition_category']})")
        print(f"✅ Grip multiplier: {tire_info['grip_multiplier']:.2f}")
        
        # Load car data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        car_data = garage_data[0]
        
        # Test tire wear calculation
        race_time = 45  # 45 seconds race
        wear_result = tire_system.apply_tire_wear(0, race_time, car_data, "aggressive_driving")
        print(f"✅ Tire wear applied: {wear_result['wear_applied']:.2f}")
        print(f"✅ New tire condition: {wear_result['new_condition']:.1f}%")
        print(f"✅ Performance impact: {wear_result['performance_impact']:.2f}")
        
        if wear_result['needs_replacement']:
            print("⚠️  Tires need replacement!")
        
        return True
        
    except Exception as e:
        print(f"❌ Tire system test failed: {e}")
        return False

def test_maintenance_system():
    """Test the maintenance system"""
    print("\n=== Testing Maintenance System ===")
    
    try:
        # Initialize maintenance data
        maintenance_system.initialize_maintenance_data(0)
        
        # Load car data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        car_data = garage_data[0]
        
        # Test maintenance cost calculation
        maintenance_cost = maintenance_system.calculate_maintenance_cost(car_data)
        print(f"✅ Maintenance cost: {maintenance_cost['total_cost']}$")
        print(f"✅ Base cost: {maintenance_cost['base_cost']}$")
        print(f"✅ Condition multiplier: {maintenance_cost['condition_multiplier']:.2f}")
        
        # Test maintenance due check
        is_due = maintenance_system.is_maintenance_due(0)
        print(f"✅ Maintenance due: {is_due}")
        
        # Test insurance plans
        for plan_type, plan_info in maintenance_system.insurance_plans.items():
            print(f"✅ Insurance {plan_type}: {plan_info['name']} - {plan_info['monthly_cost']}$ ({int(plan_info['coverage']*100)}% coverage)")
        
        return True
        
    except Exception as e:
        print(f"❌ Maintenance system test failed: {e}")
        return False

def test_economic_balance():
    """Test economic balance of the game"""
    print("\n=== Testing Economic Balance ===")
    
    try:
        # Load current profile data
        with open('data/profile.json', 'r') as f:
            profile_data = json.load(f)
        
        current_money = profile_data.get('money', 0)
        print(f"✅ Current player money: {current_money}$")
        
        # Load part prices from shop
        with open('data/shop_data.json', 'r') as f:
            shop_data = json.load(f)
        
        parts_data = shop_data[0]
        
        # Calculate average part prices
        total_parts = 0
        total_value = 0
        
        for category, parts_list in parts_data.items():
            for part in parts_list:
                total_parts += 1
                total_value += part.get('value', 0)
        
        avg_part_price = total_value / total_parts if total_parts > 0 else 0
        print(f"✅ Average part price: {avg_part_price:.0f}$")
        
        # Simulate race rewards (level 3 win) - updated formula
        base_win_reward = 150 + (3 * 100)  # Level 3 (rebalanced)
        time_bonus = 1.4  # Good time (rebalanced)
        level_multiplier = 1.36  # Level 3 multiplier (rebalanced)
        difficulty_bonus = 1.2  # Moderate difficulty
        
        simulated_reward = int(base_win_reward * time_bonus * level_multiplier * difficulty_bonus)
        print(f"✅ Simulated race reward (Level 3 win): {simulated_reward}$")
        
        # Economic ratios
        races_for_avg_part = avg_part_price / simulated_reward if simulated_reward > 0 else float('inf')
        print(f"✅ Races needed for average part: {races_for_avg_part:.1f}")
        
        # Check if balance is reasonable (should be 2-5 races for average part)
        if 2 <= races_for_avg_part <= 5:
            print("✅ Economic balance looks good!")
        elif races_for_avg_part < 2:
            print("⚠️  Parts might be too cheap relative to race rewards")
        else:
            print("⚠️  Parts might be too expensive relative to race rewards")
        
        return True
        
    except Exception as e:
        print(f"❌ Economic balance test failed: {e}")
        return False

def test_save_compatibility():
    """Test save system compatibility"""
    print("\n=== Testing Save System Compatibility ===")
    
    try:
        # Check if all required data structures exist
        with open('data/profile.json', 'r') as f:
            profile_data = json.load(f)
        
        required_structures = [
            'money', 'level', 'inventory', 'usage_data'
        ]
        
        for structure in required_structures:
            if structure in profile_data:
                print(f"✅ {structure} structure exists")
            else:
                print(f"⚠️  {structure} structure missing")
        
        # Check new structures
        new_structures = [
            'fuel_data', 'tire_data', 'maintenance_data'
        ]
        
        for structure in new_structures:
            if structure in profile_data:
                print(f"✅ New {structure} structure exists")
            else:
                print(f"ℹ️  New {structure} structure will be created on first use")
        
        return True
        
    except Exception as e:
        print(f"❌ Save compatibility test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🏁 Enhanced Car Racing Game - System Tests")
    print("=" * 50)
    
    tests = [
        test_valuation_system,
        test_fuel_system,
        test_tire_system,
        test_maintenance_system,
        test_economic_balance,
        test_save_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All systems working correctly!")
        return True
    else:
        print("⚠️  Some systems need attention")
        return False

if __name__ == "__main__":
    main()
