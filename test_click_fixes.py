#!/usr/bin/env python3
"""
Test script to verify that click fixes work correctly
Tests that components use MOUSEBUTTONDOWN events instead of get_pressed()
"""

import pygame
import time
from ui_components import TextButton, CarCard
from shop import ShopCard, TabButton

def test_text_button_click_handling():
    """Test that TextButton handles clicks correctly with events"""
    print("=== Testing TextButton Click Handling ===")
    
    try:
        pygame.init()
        
        click_count = 0
        def test_action():
            nonlocal click_count
            click_count += 1
        
        button = TextButton("Test Button", 100, 100, action=test_action)
        
        # Test event-based clicking
        mouse_pos = (150, 120)  # Inside button
        
        # Create a MOUSEBUTTONDOWN event
        click_event = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=mouse_pos)
        
        # Test handle_event method
        result = button.handle_event(click_event, mouse_pos)
        
        if not result:
            print("❌ Button didn't register click event")
            return False
        
        if click_count != 1:
            print(f"❌ Action called {click_count} times, expected 1")
            return False
        
        print("✅ TextButton event-based clicking works")
        
        # Test that multiple rapid events don't cause multiple clicks (debouncing)
        time.sleep(0.1)  # Wait less than debounce time
        
        click_event2 = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=mouse_pos)
        button.handle_event(click_event2, mouse_pos)
        
        if click_count > 1:
            print("❌ Button debouncing not working")
            return False
        
        print("✅ TextButton debouncing works")
        return True
        
    except Exception as e:
        print(f"❌ Error testing TextButton: {e}")
        return False
    finally:
        pygame.quit()

def test_car_card_click_handling():
    """Test that CarCard handles clicks correctly with events"""
    print("\n=== Testing CarCard Click Handling ===")
    
    try:
        pygame.init()
        
        # Create test car data
        car_data = {
            "name": "test_car",
            "index": 0,
            "weight": 1000
        }
        
        card = CarCard(car_data, 100, 100, 200, 150)
        
        # Test event-based clicking
        mouse_pos = (150, 120)  # Inside card
        
        # Create a MOUSEBUTTONDOWN event
        click_event = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=mouse_pos)
        
        # Test handle_event method
        result = card.handle_event(click_event, mouse_pos)
        
        if not result:
            print("❌ CarCard didn't register click event")
            return False
        
        print("✅ CarCard event-based clicking works")
        
        # Test clicking outside card
        outside_pos = (50, 50)  # Outside card
        result2 = card.handle_event(click_event, outside_pos)
        
        if result2:
            print("❌ CarCard registered click outside bounds")
            return False
        
        print("✅ CarCard boundary detection works")
        return True
        
    except Exception as e:
        print(f"❌ Error testing CarCard: {e}")
        return False
    finally:
        pygame.quit()

def test_shop_card_click_handling():
    """Test that ShopCard handles clicks correctly with events"""
    print("\n=== Testing ShopCard Click Handling ===")
    
    try:
        pygame.init()
        
        # Create test item data
        item_data = {
            "name": "Test Item",
            "value": 1000,
            "description": "Test description"
        }
        
        card = ShopCard(item_data, 100, 100, 200, 150, "part", False)
        
        # Test event-based clicking
        mouse_pos = (150, 120)  # Inside card
        
        # Create a MOUSEBUTTONDOWN event
        click_event = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=mouse_pos)
        
        # Test handle_event method
        result = card.handle_event(click_event, mouse_pos)
        
        if not result:
            print("❌ ShopCard didn't register click event")
            return False
        
        print("✅ ShopCard event-based clicking works")
        return True
        
    except Exception as e:
        print(f"❌ Error testing ShopCard: {e}")
        return False
    finally:
        pygame.quit()

def test_tab_button_click_handling():
    """Test that TabButton handles clicks correctly with events"""
    print("\n=== Testing TabButton Click Handling ===")
    
    try:
        pygame.init()
        
        tab = TabButton("Test Tab", 100, 100, 150, 50)
        
        # Test event-based clicking
        mouse_pos = (150, 120)  # Inside tab
        
        # Create a MOUSEBUTTONDOWN event
        click_event = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=mouse_pos)
        
        # Test handle_event method
        result = tab.handle_event(click_event, mouse_pos)
        
        if not result:
            print("❌ TabButton didn't register click event")
            return False
        
        print("✅ TabButton event-based clicking works")
        return True
        
    except Exception as e:
        print(f"❌ Error testing TabButton: {e}")
        return False
    finally:
        pygame.quit()

def test_legacy_compatibility():
    """Test that legacy mouse_click parameter still works"""
    print("\n=== Testing Legacy Compatibility ===")
    
    try:
        pygame.init()
        
        button = TextButton("Test Button", 100, 100)
        
        # Test legacy update method with mouse_click
        mouse_pos = (150, 120)  # Inside button
        mouse_click = (True, False, False)  # Left button pressed
        
        # This should still work for backward compatibility
        button.update(mouse_pos, mouse_click)
        
        print("✅ Legacy mouse_click parameter compatibility works")
        
        # Test CarCard legacy compatibility
        car_data = {"name": "test", "index": 0, "weight": 1000}
        card = CarCard(car_data, 100, 100, 200, 150)
        
        result = card.update(mouse_pos, mouse_click)
        if not result:
            print("❌ CarCard legacy compatibility broken")
            return False
        
        print("✅ CarCard legacy compatibility works")
        return True
        
    except Exception as e:
        print(f"❌ Error testing legacy compatibility: {e}")
        return False
    finally:
        pygame.quit()

def test_rapid_clicking_prevention():
    """Test that rapid clicking is properly prevented"""
    print("\n=== Testing Rapid Clicking Prevention ===")
    
    try:
        pygame.init()
        
        click_count = 0
        def count_clicks():
            nonlocal click_count
            click_count += 1
        
        button = TextButton("Test Button", 100, 100, action=count_clicks)
        mouse_pos = (150, 120)
        
        # Simulate rapid clicks
        for i in range(5):
            click_event = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=mouse_pos)
            button.handle_event(click_event, mouse_pos)
        
        # Should only register one click due to debouncing
        if click_count > 1:
            print(f"❌ Rapid clicking not prevented: {click_count} clicks registered")
            return False
        
        print("✅ Rapid clicking prevention works")
        
        # Wait for debounce period and try again
        time.sleep(0.3)  # Wait longer than debounce time
        
        click_event = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=mouse_pos)
        button.handle_event(click_event, mouse_pos)
        
        if click_count != 2:
            print(f"❌ Click after debounce period failed: {click_count} total clicks")
            return False
        
        print("✅ Click after debounce period works")
        return True
        
    except Exception as e:
        print(f"❌ Error testing rapid clicking prevention: {e}")
        return False
    finally:
        pygame.quit()

def main():
    """Run all click fix tests"""
    print("Click Fixes Test Suite")
    print("=" * 40)
    
    tests = [
        test_text_button_click_handling,
        test_car_card_click_handling,
        test_shop_card_click_handling,
        test_tab_button_click_handling,
        test_legacy_compatibility,
        test_rapid_clicking_prevention
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} CRASHED: {e}")
    
    print("\n" + "=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All click fixes working correctly!")
        print("\nFixed issues:")
        print("1. ✅ Components use MOUSEBUTTONDOWN events")
        print("2. ✅ Rapid clicking prevention works")
        print("3. ✅ Legacy compatibility maintained")
        print("4. ✅ Proper boundary detection")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
