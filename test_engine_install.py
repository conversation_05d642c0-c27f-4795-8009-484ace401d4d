#!/usr/bin/env python3
"""
Test script to debug engine installation issues
"""

import json
from garage_screen import validate_part_compatibility
from car_compatibility import car_compatibility

def test_engine_installation():
    """Test engine installation process step by step"""
    print("=== Testing Engine Installation Process ===")
    
    # Load current data
    try:
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        with open('data/profile.json', 'r') as f:
            profile_data = json.load(f)
        
        with open('data/shop_data.json', 'r') as f:
            shop_data = json.load(f)
        
        parts_data = shop_data[0]  # Parts section
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    # Get current car
    car_index = 0
    current_car = garage_data[car_index]
    car_name = current_car["name"]
    
    print(f"Current car: {car_name}")
    print(f"Current engine: {current_car['parts']['engine']['name']}")
    
    # Get owned engines from inventory
    owned_engines = profile_data["inventory"]["owned_parts"]["engine"]
    print(f"Owned engines: {owned_engines}")
    
    # Test each owned engine
    for engine_name in owned_engines:
        print(f"\n--- Testing engine: {engine_name} ---")
        
        # Find engine data in shop
        engine_data = None
        for engine in parts_data["engine"]:
            if engine["name"] == engine_name:
                engine_data = engine
                break
        
        if not engine_data:
            print(f"❌ Engine '{engine_name}' not found in shop data")
            continue
        
        print(f"✅ Found engine data: {engine_data}")
        
        # Test compatibility
        is_compatible, reason = car_compatibility.is_part_compatible(car_name, engine_name, "engine")
        print(f"Compatibility check: {is_compatible} - {reason}")
        
        # Test validation function
        is_valid, validation_msg = validate_part_compatibility(current_car, engine_data, "engine")
        print(f"Validation check: {is_valid} - {validation_msg}")
        
        # Test ownership check
        if engine_name in owned_engines:
            print("✅ Player owns this engine")
        else:
            print("❌ Player doesn't own this engine")

def test_compatible_engines():
    """Test which engines are compatible with current car"""
    print("\n=== Testing Compatible Engines ===")
    
    try:
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        car_name = garage_data[0]["name"]
        compatible_engines = car_compatibility.get_compatible_parts(car_name, "engine")
        
        print(f"Car: {car_name}")
        print(f"Compatible engines: {compatible_engines}")
        
        # Check which compatible engines are available in shop
        with open('data/shop_data.json', 'r') as f:
            shop_data = json.load(f)
        
        available_engines = [engine["name"] for engine in shop_data[0]["engine"]]
        print(f"Available engines in shop: {available_engines}")
        
        # Find intersection
        compatible_and_available = [engine for engine in compatible_engines if engine in available_engines]
        print(f"Compatible AND available engines: {compatible_and_available}")
        
    except Exception as e:
        print(f"Error: {e}")

def test_manual_installation():
    """Test manual installation of Tuned I4 engine"""
    print("\n=== Testing Manual Installation ===")
    
    try:
        # Load data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        with open('data/shop_data.json', 'r') as f:
            shop_data = json.load(f)
        
        # Find Tuned I4 engine data
        tuned_i4_data = None
        for engine in shop_data[0]["engine"]:
            if engine["name"] == "Tuned I4":
                tuned_i4_data = engine
                break
        
        if not tuned_i4_data:
            print("❌ Tuned I4 not found in shop data")
            return
        
        print(f"Tuned I4 data: {tuned_i4_data}")
        
        # Test installation
        car_index = 0
        current_car = garage_data[car_index]
        
        # Simulate the installation process
        print("Simulating installation...")
        
        # 1. Compatibility check
        is_compatible, reason = validate_part_compatibility(current_car, tuned_i4_data, "engine")
        print(f"1. Compatibility: {is_compatible} - {reason}")
        
        if is_compatible:
            # 2. Update equipped parts (simulate what garage does)
            equipped_parts = current_car["parts"].copy()
            equipped_parts["engine"] = tuned_i4_data
            
            print("2. ✅ Would install Tuned I4 engine")
            print(f"   New engine: {equipped_parts['engine']['name']}")
            print(f"   New horsepower: {equipped_parts['engine']['horsepower']} HP")
        else:
            print("2. ❌ Installation would fail")
        
    except Exception as e:
        print(f"Error in manual installation test: {e}")

if __name__ == "__main__":
    try:
        test_engine_installation()
        test_compatible_engines()
        test_manual_installation()
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
