#!/usr/bin/env python3
"""
Test script to verify the race calculation fix
"""

def test_performance_bonus_calculation():
    """Test the performance bonus calculation with various scenarios"""
    print("=== Testing Performance Bonus Calculation ===")
    
    def calculate_performance_bonus(opponent_time, player_time):
        """Simulate the fixed performance bonus calculation"""
        if player_time is not None and player_time > 0:
            # Player finished but lost - calculate based on time difference
            performance_bonus = max(1.0, 1.5 - (opponent_time / player_time))
        else:
            # Player didn't finish - minimal bonus
            performance_bonus = 1.0
        return performance_bonus
    
    # Test cases
    test_cases = [
        # (opponent_time, player_time, expected_scenario)
        (30.0, 35.0, "Player finished but lost (close race)"),
        (25.0, 40.0, "Player finished but lost (big gap)"),
        (30.0, None, "Player didn't finish"),
        (30.0, 0, "Player time is zero"),
        (20.0, 30.0, "Player finished but lost (normal case)"),
    ]
    
    all_passed = True
    
    for opponent_time, player_time, scenario in test_cases:
        try:
            bonus = calculate_performance_bonus(opponent_time, player_time)
            print(f"✅ {scenario}: bonus = {bonus:.2f}")
            
            # Validate bonus is reasonable
            if bonus < 1.0 or bonus > 1.5:
                print(f"  ⚠️  Bonus {bonus:.2f} is outside expected range [1.0, 1.5]")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {scenario}: ERROR - {e}")
            all_passed = False
    
    return all_passed

def test_race_time_safety():
    """Test race time safety checks"""
    print("\n=== Testing Race Time Safety Checks ===")
    
    def safe_fuel_tire_consumption(race_time):
        """Simulate the fixed fuel/tire consumption logic"""
        if race_time and race_time > 0:
            # Simulate fuel and tire consumption
            fuel_consumed = race_time * 0.1  # Mock calculation
            tire_wear = race_time * 0.5      # Mock calculation
            return fuel_consumed, tire_wear
        else:
            # No consumption if invalid race time
            return 0, 0
    
    test_cases = [
        (30.0, "Normal race time"),
        (None, "None race time"),
        (0, "Zero race time"),
        (-5.0, "Negative race time"),
        (120.0, "Long race time"),
    ]
    
    all_passed = True
    
    for race_time, scenario in test_cases:
        try:
            fuel_consumed, tire_wear = safe_fuel_tire_consumption(race_time)
            print(f"✅ {scenario}: fuel={fuel_consumed:.1f}, tire_wear={tire_wear:.1f}")
            
            # Validate results are non-negative
            if fuel_consumed < 0 or tire_wear < 0:
                print(f"  ⚠️  Negative consumption values detected")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {scenario}: ERROR - {e}")
            all_passed = False
    
    return all_passed

def main():
    """Run all tests"""
    print("🏁 Race Calculation Fix - Test Suite")
    print("=" * 50)
    
    tests = [
        test_performance_bonus_calculation,
        test_race_time_safety
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Race calculation fixes are working correctly!")
        print("The TypeError should no longer occur during races.")
        return True
    else:
        print("⚠️  Some issues remain - check the output above")
        return False

if __name__ == "__main__":
    main()
