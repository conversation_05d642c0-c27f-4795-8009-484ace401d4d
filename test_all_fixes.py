#!/usr/bin/env python3
"""
Comprehensive test suite to verify all bug fixes and improvements
"""

import sys
import os
import subprocess
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_test_suite(test_file, description):
    """Run a test suite and return results"""
    print(f"\n{'='*60}")
    print(f"🔧 Running {description}")
    print(f"{'='*60}")
    
    try:
        start_time = time.time()
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, text=True, timeout=120)
        end_time = time.time()
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        success = result.returncode == 0
        duration = end_time - start_time
        
        if success:
            print(f"✅ {description} PASSED in {duration:.2f}s")
        else:
            print(f"❌ {description} FAILED in {duration:.2f}s")
            print(f"Return code: {result.returncode}")
        
        return success, duration
        
    except subprocess.TimeoutExpired:
        print(f"❌ {description} TIMED OUT after 120s")
        return False, 120.0
    except Exception as e:
        print(f"❌ {description} CRASHED: {e}")
        return False, 0.0

def test_basic_functionality():
    """Test basic game functionality"""
    print("\n=== Testing Basic Game Functionality ===")
    
    try:
        # Test imports
        from game import Game
        from fuel_system import fuel_system
        from tire_system import tire_system
        from maintenance_system import maintenance_system
        
        # Test basic initialization
        game = Game()
        
        # Test level calculation
        exp_level_5 = game.calculate_exp_required_for_level(5)
        if exp_level_5 <= 0:
            print("❌ ERROR: Level calculation failed")
            return False
        
        # Test system initialization
        fuel_init = fuel_system.initialize_fuel_data(0)
        tire_init = tire_system.initialize_tire_data(0)
        maint_init = maintenance_system.initialize_maintenance_data(0)
        
        if not all([fuel_init, tire_init, maint_init]):
            print("❌ ERROR: System initialization failed")
            return False
        
        print("✅ Basic functionality working")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Basic functionality test failed: {e}")
        return False

def main():
    """Run comprehensive test suite"""
    print("COMPREHENSIVE BUG FIX VERIFICATION")
    print("=" * 80)
    print("This test suite verifies all bug fixes and improvements made to Xtra Cars")
    print("=" * 80)
    
    # Test suites to run
    test_suites = [
        ("test_level_system.py", "Level System Tests"),
        ("test_game_systems.py", "Game Systems Tests"),
        ("test_performance_stability.py", "Performance & Stability Tests"),
        ("test_bug_fixes.py", "Bug Fixes Tests"),
        ("test_complete_fix.py", "Complete Fix Tests")
    ]
    
    # Track results
    total_tests = len(test_suites) + 1  # +1 for basic functionality
    passed_tests = 0
    total_duration = 0.0
    
    # Run basic functionality test first
    if test_basic_functionality():
        passed_tests += 1
    
    # Run all test suites
    for test_file, description in test_suites:
        if os.path.exists(test_file):
            success, duration = run_test_suite(test_file, description)
            total_duration += duration
            if success:
                passed_tests += 1
        else:
            print(f"\n❌ Test file {test_file} not found - skipping {description}")
    
    # Final summary
    print("\n" + "=" * 80)
    print("FINAL TEST RESULTS")
    print("=" * 80)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Total Duration: {total_duration:.2f}s")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\nALL TESTS PASSED!")
        print("Bug fixes verified successfully")
        print("Game systems working correctly")
        print("Performance optimized")
        print("Stability improved")
        print("Error handling robust")

        print("\nSUMMARY OF FIXES APPLIED:")
        print("=" * 50)
        print("1. Fixed resource_path() usage in fuel_system.py")
        print("2. Removed debug print statements from game.py")
        print("3. Fixed file path consistency across all systems")
        print("4. Eliminated code duplication in race logic")
        print("5. Fixed level progression calculation bug")
        print("6. Fixed recursion issue in system initialization")
        print("7. Added performance caching to fuel system")
        print("8. Improved error handling and recovery")
        print("9. Fixed unused variable warnings")
        print("10. Optimized file I/O operations")

        print("\nGAME IS NOW READY FOR PRODUCTION!")
        
    else:
        print(f"\n{total_tests - passed_tests} TESTS FAILED")
        print("Some issues still need to be addressed")

        failed_tests = total_tests - passed_tests
        if failed_tests <= 2:
            print("Most fixes are working - minor issues remain")
        else:
            print("Significant issues detected - review needed")
    
    print("\n" + "=" * 80)
    return passed_tests == total_tests

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
