#!/usr/bin/env python3
"""
Test script to validate the progress bar fix and game balance improvements
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_progress_bar_bounds():
    """Test that progress bar handles edge cases correctly"""
    print("🧪 Testing Progress Bar Bounds Checking...")
    
    try:
        from ui import ProgressBar
        
        # Test normal case
        pb1 = ProgressBar(0, 0, 100, 20, 50, 100)
        print("  ✅ Normal case (50/100): Created successfully")
        
        # Test edge case: progress equals max_value
        pb2 = ProgressBar(0, 0, 100, 20, 100, 100)
        print("  ✅ Edge case (100/100): Created successfully")
        
        # Test edge case: progress exceeds max_value
        pb3 = ProgressBar(0, 0, 100, 20, 150, 100)
        print("  ✅ Overflow case (150/100): Created successfully")
        
        # Test edge case: zero max_value
        pb4 = ProgressBar(0, 0, 100, 20, 50, 0)
        print("  ✅ Zero max case (50/0): Created successfully")
        
        # Test edge case: negative progress
        pb5 = ProgressBar(0, 0, 100, 20, -10, 100)
        print("  ✅ Negative progress (-10/100): Created successfully")
        
        print("  ✅ Progress bar bounds checking: PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Progress bar test failed: {e}")
        return False

def test_game_balance():
    """Test that game balance has been improved"""
    print("\n🧪 Testing Game Balance Improvements...")
    
    try:
        # Load opponent data
        with open('data/oponent_levels.json', 'r') as f:
            opponents = json.load(f)
        
        # Load player starting car data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        player_car = garage_data[0]
        
        # Calculate player power-to-weight ratio
        def calculate_power_to_weight(car_data):
            engine = car_data['parts']['engine']
            base_hp = engine['horsepower']
            total_boost = 0
            total_weight = car_data['weight'] + engine['weight']
            
            for part_type, part in car_data['parts'].items():
                if part_type != 'engine' and part is not None:
                    boost = part.get('horsepower_boost_percentage', 0)
                    total_boost += boost
                    total_weight += part.get('weight', 0)
            
            total_hp = base_hp * (1 + total_boost / 100)
            return total_hp / total_weight if total_weight > 0 else 0
        
        player_ratio = calculate_power_to_weight(player_car)
        print(f"  📊 Player power-to-weight ratio: {player_ratio:.3f}")
        
        # Check opponent progression
        ratios = []
        for i, opponent in enumerate(opponents[:5]):  # Check first 5 levels
            # Calculate opponent power-to-weight
            engine = opponent['parts']['engine']
            base_hp = engine['horsepower']
            total_boost = 0
            total_weight = opponent['weight'] + engine['weight']
            
            for part_type, part in opponent['parts'].items():
                if part_type != 'engine' and part is not None:
                    boost = part.get('horsepower_boost_percentage', 0)
                    total_boost += boost
                    total_weight += part.get('weight', 0)
            
            total_hp = base_hp * (1 + total_boost / 100)
            ratio = total_hp / total_weight if total_weight > 0 else 0
            ratios.append(ratio)
            
            print(f"  📊 Level {i+1} opponent ratio: {ratio:.3f}")
        
        # Validate balance improvements
        balance_issues = []
        
        # Level 1 should be easier than player
        if ratios[0] >= player_ratio:
            balance_issues.append("Level 1 should be easier than player")
        
        # Level 2 should be close to player level (within 10%)
        level2_diff = abs(ratios[1] - player_ratio) / player_ratio
        if level2_diff > 0.15:  # Allow 15% difference
            balance_issues.append(f"Level 2 too different from player ({level2_diff:.1%})")
        
        # Check for reasonable progression (no jumps > 30%)
        for i in range(1, len(ratios)):
            increase = (ratios[i] - ratios[i-1]) / ratios[i-1]
            if increase > 0.35:  # Allow up to 35% increase per level
                balance_issues.append(f"Level {i+1} jump too large ({increase:.1%})")
        
        if balance_issues:
            print("  ⚠️  Balance issues found:")
            for issue in balance_issues:
                print(f"    - {issue}")
            return False
        else:
            print("  ✅ Game balance improvements: PASSED")
            return True
            
    except Exception as e:
        print(f"  ❌ Game balance test failed: {e}")
        return False

def test_file_references():
    """Test that all file references are correct"""
    print("\n🧪 Testing File References...")
    
    required_files = [
        'data/garage.json',
        'data/profile.json', 
        'data/oponent_levels.json',
        'data/shop_data.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("  ❌ Missing required files:")
        for file_path in missing_files:
            print(f"    - {file_path}")
        return False
    else:
        print("  ✅ All required files present: PASSED")
        return True

def main():
    """Run all validation tests"""
    print("🔧 Validating Bug Fixes and Improvements")
    print("=" * 50)
    
    tests = [
        test_file_references,
        test_progress_bar_bounds,
        test_game_balance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes validated successfully!")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
