#!/usr/bin/env python3
"""
Test script to verify that ui.py uses resource_path correctly for all data files
"""

import os
import json
import pygame
from utils import resource_path, get_user_data_folder

def test_ui_functions_import():
    """Test that ui.py functions can be imported without errors"""
    print("=== Testing UI Functions Import ===")
    
    try:
        from ui import save_new_username, save_car_selection
        print("✅ UI functions imported successfully")
        return True
    except Exception as e:
        print(f"❌ Error importing UI functions: {e}")
        return False

def test_save_new_username():
    """Test save_new_username function with resource_path"""
    print("\n=== Testing save_new_username Function ===")
    
    try:
        # Initialize pygame for testing
        pygame.init()
        
        # Backup original profile
        profile_path = resource_path('data/profile.json')
        with open(profile_path, 'r') as f:
            original_profile = json.load(f)
        
        original_username = original_profile.get('username', 'Unknown')
        print(f"Original username: {original_username}")
        
        # Test changing username
        from ui import save_new_username
        test_username = "Test User"
        save_new_username(test_username)
        
        # Verify change
        with open(profile_path, 'r') as f:
            updated_profile = json.load(f)
        
        if updated_profile.get('username') == test_username:
            print(f"✅ Username successfully changed to: {test_username}")
        else:
            print(f"❌ Username not changed correctly")
            return False
        
        # Restore original username
        save_new_username(original_username)
        print(f"✅ Username restored to: {original_username}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing save_new_username: {e}")
        return False
    finally:
        pygame.quit()

def test_save_car_selection():
    """Test save_car_selection function with resource_path"""
    print("\n=== Testing save_car_selection Function ===")
    
    try:
        # Initialize pygame for testing
        pygame.init()
        
        # Backup original profile
        profile_path = resource_path('data/profile.json')
        with open(profile_path, 'r') as f:
            original_profile = json.load(f)
        
        original_car_index = original_profile.get('cars', {}).get('selected_car', 0)
        print(f"Original selected car index: {original_car_index}")
        
        # Test changing car selection
        from ui import save_car_selection
        test_car_index = 0 if original_car_index != 0 else 1
        save_car_selection(test_car_index)
        
        # Verify change
        with open(profile_path, 'r') as f:
            updated_profile = json.load(f)
        
        if updated_profile.get('cars', {}).get('selected_car') == test_car_index:
            print(f"✅ Car selection successfully changed to: {test_car_index}")
        else:
            print(f"❌ Car selection not changed correctly")
            return False
        
        # Restore original selection
        save_car_selection(original_car_index)
        print(f"✅ Car selection restored to: {original_car_index}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing save_car_selection: {e}")
        return False
    finally:
        pygame.quit()

def test_data_file_access():
    """Test that all data files are accessible through resource_path"""
    print("\n=== Testing Data File Access ===")
    
    try:
        # Test files that ui.py should access
        test_files = [
            'data/profile.json',
            'data/garage.json',
            'data/shop_data.json'
        ]
        
        for file_path in test_files:
            resource_file_path = resource_path(file_path)
            print(f"Testing {file_path} -> {resource_file_path}")
            
            if not os.path.exists(resource_file_path):
                print(f"❌ File not found: {resource_file_path}")
                return False
            
            # Try to load the file
            with open(resource_file_path, 'r') as f:
                data = json.load(f)
            
            if not data:
                print(f"❌ File is empty or invalid: {resource_file_path}")
                return False
            
            print(f"✅ File accessible: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing data file access: {e}")
        return False

def test_user_data_folder_structure():
    """Test that user data folder has correct structure"""
    print("\n=== Testing User Data Folder Structure ===")
    
    try:
        user_data_folder = get_user_data_folder()
        print(f"User data folder: {user_data_folder}")
        
        # Check if folder exists
        if not os.path.exists(user_data_folder):
            print("❌ User data folder doesn't exist")
            return False
        
        # Check for required files
        required_files = ['profile.json', 'garage.json']
        for filename in required_files:
            file_path = os.path.join(user_data_folder, filename)
            if not os.path.exists(file_path):
                print(f"❌ Required file missing: {filename}")
                return False
            
            # Test file is valid JSON
            with open(file_path, 'r') as f:
                json.load(f)
            
            print(f"✅ File present and valid: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing user data folder structure: {e}")
        return False

def test_resource_path_redirection():
    """Test that resource_path correctly redirects user data files"""
    print("\n=== Testing Resource Path Redirection ===")
    
    try:
        user_data_folder = get_user_data_folder()
        
        # Test user data files
        user_data_files = ['data/profile.json', 'data/garage.json']
        
        for file_path in user_data_files:
            resource_file_path = resource_path(file_path)
            
            # Check if path is redirected to user folder
            if user_data_folder.lower() not in resource_file_path.lower():
                print(f"❌ File not redirected to user folder: {file_path}")
                print(f"   Expected in: {user_data_folder}")
                print(f"   Actual path: {resource_file_path}")
                return False
            
            print(f"✅ File correctly redirected: {file_path}")
        
        # Test read-only files (should NOT be redirected)
        readonly_files = ['data/shop_data.json', 'data/oponent_levels.json']
        
        for file_path in readonly_files:
            resource_file_path = resource_path(file_path)
            
            # These should NOT be in user folder
            if user_data_folder.lower() in resource_file_path.lower():
                print(f"⚠️  Read-only file incorrectly redirected: {file_path}")
                # This is not necessarily an error, but worth noting
            else:
                print(f"✅ Read-only file correctly handled: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing resource path redirection: {e}")
        return False

def main():
    """Run all UI resource path fix tests"""
    print("UI Resource Path Fix Test Suite")
    print("=" * 50)
    
    tests = [
        test_ui_functions_import,
        test_data_file_access,
        test_user_data_folder_structure,
        test_resource_path_redirection,
        test_save_new_username,
        test_save_car_selection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All UI resource path fixes working correctly!")
        print("\nFixed issues:")
        print("1. ✅ ui.py now uses resource_path for all data files")
        print("2. ✅ No more 'FileNotFoundError' when accessing garage/profile")
        print("3. ✅ User data correctly stored in writable location")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
