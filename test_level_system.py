#!/usr/bin/env python3
"""
Test suite for level and experience system verification
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game import Game

def test_exp_calculation():
    """Test experience calculation for different levels"""
    print("=== Testing Experience Calculation ===")
    
    game = Game()
    
    # Test basic levels
    test_levels = [1, 2, 5, 10, 25, 50, 100]
    
    for level in test_levels:
        exp_required = game.calculate_exp_required_for_level(level)
        print(f"Level {level}: {exp_required} EXP required")
        
        # Verify that higher levels require more EXP
        if level > 1:
            prev_exp = game.calculate_exp_required_for_level(level - 1)
            if exp_required <= prev_exp:
                print(f"❌ ERROR: Level {level} requires less EXP than level {level-1}")
                return False
    
    print("✅ Experience calculation is monotonically increasing")
    return True

def test_level_progression():
    """Test level progression logic"""
    print("\n=== Testing Level Progression ===")

    game = Game()

    # Test data
    test_user_data = {
        'level': {'current': 1, 'exp': 0, 'required_to_next_level': 0},
        'money': 1000,
        'inventory': {'owned_parts': {'engine': [], 'turbo': [], 'intercooler': [], 'ecu': []}}
    }

    # Test normal progression
    player_level = 1
    player_exp = 300  # Should be enough for level 2 (requires 274)

    print(f"Testing with player_level={player_level}, player_exp={player_exp}")
    print(f"Level 2 requires: {game.calculate_exp_required_for_level(2)} EXP")

    new_level, _, levels_gained, rewards = game.handle_level_up(player_exp, player_level, test_user_data)

    print(f"Result: new_level={new_level}, levels_gained={levels_gained}")

    if new_level <= player_level:
        print(f"❌ ERROR: Level didn't increase with sufficient EXP")
        print(f"  Expected level > {player_level}, got {new_level}")
        return False

    if not levels_gained:
        print(f"❌ ERROR: No levels gained despite sufficient EXP")
        return False

    print(f"✅ Level progression: {player_level} → {new_level}")
    print(f"✅ Levels gained: {levels_gained}")
    print(f"✅ Money reward: {rewards['money']}")

    return True

def test_reward_calculation():
    """Test reward calculation for different levels"""
    print("\n=== Testing Reward Calculation ===")
    
    game = Game()
    
    # Test rewards for different levels
    test_levels = [1, 5, 10, 25, 50, 100]
    
    for level in test_levels:
        rewards = game.calculate_level_up_rewards(level)
        
        print(f"Level {level} rewards:")
        print(f"  Money: {rewards['money']}")
        print(f"  Parts: {len(rewards['unlocked_parts'])}")
        print(f"  Achievements: {len(rewards['achievements'])}")
        
        # Verify money reward is positive
        if rewards['money'] <= 0:
            print(f"❌ ERROR: Level {level} has non-positive money reward")
            return False
        
        # Verify milestone bonuses
        if level % 25 == 0:
            if not rewards['special_bonuses']:
                print(f"❌ ERROR: Level {level} missing milestone bonus")
                return False
    
    print("✅ Reward calculation working correctly")
    return True

def test_fix_level_progression():
    """Test level progression fix function"""
    print("\n=== Testing Level Progression Fix ===")
    
    game = Game()
    
    # Test corrupted data
    corrupted_data = {
        'level': {
            'current': 1,
            'exp': 500,  # More than enough for multiple levels
            'required_to_next_level': 100  # Incorrect value
        }
    }
    
    fixed_data = game.fix_level_progression(corrupted_data)
    
    # Check if level was corrected
    if fixed_data['level']['current'] == 1:
        print(f"❌ ERROR: Level not corrected despite high EXP")
        return False
    
    # Check if required_to_next_level was corrected
    expected_required = game.calculate_exp_required_for_level(fixed_data['level']['current'] + 1)
    if fixed_data['level']['required_to_next_level'] != expected_required:
        print(f"❌ ERROR: required_to_next_level not corrected")
        return False
    
    print(f"✅ Level progression fix: 1 → {fixed_data['level']['current']}")
    print(f"✅ Required EXP corrected: {fixed_data['level']['required_to_next_level']}")
    
    return True

def test_edge_cases():
    """Test edge cases in level system"""
    print("\n=== Testing Edge Cases ===")
    
    game = Game()
    
    # Test level 1 (should return 0)
    exp_level_1 = game.calculate_exp_required_for_level(1)
    if exp_level_1 != 0:
        print(f"❌ ERROR: Level 1 should require 0 EXP, got {exp_level_1}")
        return False
    
    # Test level 0 (should return 0)
    exp_level_0 = game.calculate_exp_required_for_level(0)
    if exp_level_0 != 0:
        print(f"❌ ERROR: Level 0 should require 0 EXP, got {exp_level_0}")
        return False
    
    # Test very high level (should not crash)
    try:
        exp_level_1000 = game.calculate_exp_required_for_level(1000)
        if exp_level_1000 <= 0:
            print(f"❌ ERROR: Very high level returned non-positive EXP")
            return False
    except Exception as e:
        print(f"❌ ERROR: High level calculation crashed: {e}")
        return False
    
    print("✅ Edge cases handled correctly")
    return True

def main():
    """Run all level system tests"""
    print("Level System Verification Test Suite")
    print("=" * 60)
    
    tests = [
        test_exp_calculation,
        test_level_progression,
        test_reward_calculation,
        test_fix_level_progression,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} CRASHED: {e}")
    
    print("\n" + "=" * 60)
    print(f"🔧 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("ALL LEVEL SYSTEM TESTS PASSED!")
        print("Experience calculation: WORKING")
        print("Level progression: WORKING")
        print("Reward system: WORKING")
        print("Data corruption fix: WORKING")
        print("Edge cases: HANDLED")
    else:
        print("SOME TESTS FAILED - Level system needs fixes")
    
    return passed == total

if __name__ == '__main__':
    main()
