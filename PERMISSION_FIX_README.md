# Rozwiązanie problemu uprawnień w folderze Program Files

## Problem
Po zainstalowaniu gry w folderze systemowym `C:\Program Files (x86)\Xtra Cars`, gra wyrzucała błąd uprawnień:

```
PermissionError: [Errno 13] Permission denied: 'C:\\Program Files (x86)\\Xtra Cars\\_internal\\data/profile.json'
```

## Przyczyna
Aplikacje zainstalowane w folderach systemowych (`Program Files`, `Program Files (x86)`) nie mają uprawnień do zapisu w tych lokalizacjach bez uprawnień administratora. Gra próbowała zapisywać dane użytkownika (profile.json, garage.json) bezpośrednio w folderze instalacyjnym.

## Rozwiązanie

### 1. Zmodyfikowana funkcja `resource_path` w `utils.py`

Dodano inteligentne rozróżnienie między:
- **Plikami tylko do odczytu** (zasoby gry: o<PERSON><PERSON>, d<PERSON><PERSON><PERSON><PERSON>, dane skle<PERSON>) - pozostają w folderze instalacyjnym
- **Plikami danych użytkownika** (profile.json, garage.json) - przekierowywane do folderu użytkownika

```python
def get_user_data_folder():
    """Get the user data folder for storing writable game data"""
    appdata = os.getenv('APPDATA')
    user_data_folder = os.path.join(appdata, "Xtra Cars", "GameData")
    os.makedirs(user_data_folder, exist_ok=True)
    return user_data_folder

def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    # Check if this is a user data file that needs to be writable
    user_data_files = ['data/profile.json', 'data/garage.json']
    
    if relative_path in user_data_files:
        # For user data files, use the user data folder
        user_data_folder = get_user_data_folder()
        filename = os.path.basename(relative_path)
        user_file_path = os.path.join(user_data_folder, filename)
        
        # If the file doesn't exist in user folder, copy it from the installation folder
        if not os.path.exists(user_file_path):
            # Get the original file from installation folder
            if hasattr(sys, '_MEIPASS'):
                original_path = os.path.join(sys._MEIPASS, relative_path)
            else:
                original_path = os.path.join(os.path.abspath("."), relative_path)
            
            # Copy the original file to user folder if it exists
            if os.path.exists(original_path):
                import shutil
                shutil.copy2(original_path, user_file_path)
        
        return user_file_path
    
    # For read-only resources (images, sounds, etc.), use the original logic
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)
```

### 2. Zaktualizowane pliki

Wszystkie pliki, które operują na danych użytkownika, zostały zaktualizowane do używania `resource_path`:

- `game.py` - funkcja `single_player()` i inne operacje na profilach
- `save_system.py` - system zapisów gry
- `shop.py` - zakupy i aktualizacje profilu
- `garage_screen.py` - ekran garażu
- `fuel_system.py` - system paliwa
- `repair_ui.py` - interfejs napraw
- Pliki testowe

### 3. Lokalizacja danych użytkownika

Dane użytkownika są teraz przechowywane w:
```
%APPDATA%\Xtra Cars\GameData\
├── profile.json
└── garage.json
```

Na przykład:
```
C:\Users\<USER>\AppData\Roaming\Xtra Cars\GameData\
```

### 4. Automatyczne kopiowanie plików

Przy pierwszym uruchomieniu gry po instalacji:
1. System sprawdza, czy pliki danych użytkownika istnieją w folderze użytkownika
2. Jeśli nie, kopiuje domyślne pliki z folderu instalacyjnego
3. Wszystkie kolejne operacje odczytu/zapisu odbywają się w folderze użytkownika

## Korzyści rozwiązania

1. **Zgodność z Windows** - dane użytkownika w odpowiedniej lokalizacji
2. **Brak problemów z uprawnieniami** - folder użytkownika jest zawsze zapisywalny
3. **Zachowanie kompatybilności** - zasoby gry pozostają w folderze instalacyjnym
4. **Automatyczna migracja** - istniejące pliki są kopiowane przy pierwszym uruchomieniu
5. **Bezpieczeństwo** - dane użytkownika oddzielone od plików programu

## Testowanie

Utworzono test `test_user_data_fix.py`, który weryfikuje:
- Tworzenie folderu danych użytkownika
- Dostęp do plików profilu i garażu
- Mechanizm kopiowania plików
- Symulację scenariusza uprawnień

Wszystkie testy przechodzą pomyślnie.

## Instalacja

Po wprowadzeniu tych zmian:
1. Gra może być bezpiecznie zainstalowana w `Program Files`
2. Nie wymaga uprawnień administratora do działania
3. Dane użytkownika są bezpiecznie przechowywane w folderze użytkownika
4. System zapisów działa poprawnie

## Uwagi dla przyszłych aktualizacji

- Wszystkie nowe pliki danych użytkownika powinny być dodane do listy `user_data_files` w funkcji `resource_path`
- Zasoby tylko do odczytu (obrazy, dźwięki, dane sklepów) powinny pozostać w folderze instalacyjnym
- Zawsze używaj `resource_path()` zamiast bezpośrednich ścieżek do plików
