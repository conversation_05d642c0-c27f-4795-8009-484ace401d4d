#!/usr/bin/env python3
"""
Comprehensive test to verify all resource_path fixes across the entire codebase
"""

import os
import json
import pygame
from utils import resource_path, get_user_data_folder

def test_all_imports():
    """Test that all modules can be imported without errors"""
    print("=== Testing All Module Imports ===")
    
    try:
        # Test importing all modules that use resource_path
        import ui
        import shop
        import shop_screen
        import level_info
        import save_system
        import garage_screen
        import fuel_system
        import repair_ui
        import game
        
        print("✅ All modules imported successfully")
        return True
    except Exception as e:
        print(f"❌ Error importing modules: {e}")
        return False

def test_data_file_accessibility():
    """Test that all data files are accessible through resource_path"""
    print("\n=== Testing Data File Accessibility ===")
    
    try:
        # Test all data files used in the game
        data_files = [
            'data/profile.json',
            'data/garage.json',
            'data/shop_data.json',
            'data/oponent_levels.json'
        ]
        
        for file_path in data_files:
            resource_file_path = resource_path(file_path)
            print(f"Testing {file_path} -> {resource_file_path}")
            
            if not os.path.exists(resource_file_path):
                print(f"❌ File not found: {resource_file_path}")
                return False
            
            # Try to load the file
            with open(resource_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not data:
                print(f"❌ File is empty or invalid: {resource_file_path}")
                return False
            
            print(f"✅ File accessible: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing data file accessibility: {e}")
        return False

def test_user_data_redirection():
    """Test that user data files are correctly redirected to user folder"""
    print("\n=== Testing User Data Redirection ===")
    
    try:
        user_data_folder = get_user_data_folder()
        
        # Test user data files (should be redirected)
        user_data_files = ['data/profile.json', 'data/garage.json']
        
        for file_path in user_data_files:
            resource_file_path = resource_path(file_path)
            
            # Check if path is redirected to user folder
            if user_data_folder.lower() not in resource_file_path.lower():
                print(f"❌ User data file not redirected: {file_path}")
                print(f"   Expected in: {user_data_folder}")
                print(f"   Actual path: {resource_file_path}")
                return False
            
            print(f"✅ User data file correctly redirected: {file_path}")
        
        # Test read-only files (should NOT be redirected to user folder)
        readonly_files = ['data/shop_data.json', 'data/oponent_levels.json']
        
        for file_path in readonly_files:
            resource_file_path = resource_path(file_path)
            
            # These should be in the installation folder, not user folder
            print(f"✅ Read-only file handled: {file_path} -> {resource_file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing user data redirection: {e}")
        return False

def test_ui_functions():
    """Test UI functions that use resource_path"""
    print("\n=== Testing UI Functions ===")
    
    try:
        pygame.init()
        
        # Test save_new_username
        from ui import save_new_username, save_car_selection
        
        # Backup original data
        profile_path = resource_path('data/profile.json')
        with open(profile_path, 'r') as f:
            original_profile = json.load(f)
        
        # Test username change
        original_username = original_profile.get('username', 'Unknown')
        test_username = "Test User"
        save_new_username(test_username)
        
        # Verify change
        with open(profile_path, 'r') as f:
            updated_profile = json.load(f)
        
        if updated_profile.get('username') != test_username:
            print("❌ Username change failed")
            return False
        
        # Restore original
        save_new_username(original_username)
        print("✅ UI username function working")
        
        # Test car selection
        original_car = original_profile.get('cars', {}).get('selected_car', 0)
        test_car = 0 if original_car != 0 else 1
        save_car_selection(test_car)
        
        # Verify change
        with open(profile_path, 'r') as f:
            updated_profile = json.load(f)
        
        if updated_profile.get('cars', {}).get('selected_car') != test_car:
            print("❌ Car selection change failed")
            return False
        
        # Restore original
        save_car_selection(original_car)
        print("✅ UI car selection function working")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing UI functions: {e}")
        return False
    finally:
        pygame.quit()

def test_level_info_data():
    """Test level info screen data loading"""
    print("\n=== Testing Level Info Data Loading ===")
    
    try:
        # Test loading opponent data like level_info.py does
        with open(resource_path('data/oponent_levels.json'), 'r', encoding='utf-8') as f:
            opponents_data = json.load(f)
        
        if not opponents_data:
            print("❌ No opponent data loaded")
            return False
        
        # Test first opponent
        opponent = opponents_data[0]
        engine = opponent.get('parts', {}).get('engine', {})
        engine_name = engine.get('name', '')
        
        if not engine_name or engine_name == 'Nieznana część':
            print(f"❌ Invalid engine name: '{engine_name}'")
            return False
        
        print(f"✅ Level info data loading working - Engine: {engine_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error testing level info data: {e}")
        return False

def test_shop_functions():
    """Test shop-related functions"""
    print("\n=== Testing Shop Functions ===")
    
    try:
        # Test shop data loading like shop_screen.py does
        with open(resource_path('data/shop_data.json')) as f:
            shop_data = json.load(f)
        
        if not shop_data or len(shop_data) < 2:
            print("❌ Invalid shop data structure")
            return False
        
        parts_data = shop_data[0]
        cars_data = shop_data[1].get('cars', [])
        
        if not parts_data or not cars_data:
            print("❌ Missing parts or cars data")
            return False
        
        print("✅ Shop data loading working")
        
        # Test profile loading in shop context
        with open(resource_path('data/profile.json')) as f:
            profile_data = json.load(f)
        
        if not profile_data:
            print("❌ Profile data not loaded in shop context")
            return False
        
        print("✅ Shop profile loading working")
        return True
        
    except Exception as e:
        print(f"❌ Error testing shop functions: {e}")
        return False

def test_garage_functions():
    """Test garage-related functions"""
    print("\n=== Testing Garage Functions ===")
    
    try:
        # Test garage data loading
        with open(resource_path('data/garage.json')) as f:
            garage_data = json.load(f)
        
        if not garage_data:
            print("❌ No garage data loaded")
            return False
        
        print("✅ Garage data loading working")
        
        # Test profile loading in garage context
        with open(resource_path('data/profile.json')) as f:
            profile_data = json.load(f)
        
        if not profile_data:
            print("❌ Profile data not loaded in garage context")
            return False
        
        print("✅ Garage profile loading working")
        return True
        
    except Exception as e:
        print(f"❌ Error testing garage functions: {e}")
        return False

def main():
    """Run all comprehensive resource path tests"""
    print("Comprehensive Resource Path Fix Test Suite")
    print("=" * 60)
    
    tests = [
        test_all_imports,
        test_data_file_accessibility,
        test_user_data_redirection,
        test_level_info_data,
        test_shop_functions,
        test_garage_functions,
        test_ui_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} CRASHED: {e}")
    
    print("\n" + "=" * 60)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL RESOURCE PATH FIXES WORKING CORRECTLY!")
        print("\nFixed issues:")
        print("1. ✅ All modules use resource_path for data files")
        print("2. ✅ User data correctly redirected to writable location")
        print("3. ✅ No more FileNotFoundError after installation")
        print("4. ✅ Single player opponent data displays correctly")
        print("5. ✅ UI functions work with proper file paths")
        print("6. ✅ Shop and garage screens access data correctly")
        print("\n🎮 Game should work perfectly after installation!")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
