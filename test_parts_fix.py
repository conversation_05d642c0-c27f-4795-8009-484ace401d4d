#!/usr/bin/env python3
"""
Test script to verify that car parts are properly saved and loaded
"""

import json
from parts_requirements import parts_requirements
from garage_screen import save_car_parts

def test_parts_requirements():
    """Test the updated parts requirements system"""
    print("=== Testing Parts Requirements System ===")
    
    # Test requirements for each car model
    for car_name in ["old", "future", "Xtreme"]:
        required_parts = parts_requirements.get_required_parts(car_name)
        optional_parts = parts_requirements.get_optional_parts(car_name)
        
        print(f"\n{car_name.upper()} CAR:")
        print(f"  Required parts: {required_parts}")
        print(f"  Optional parts: {optional_parts}")

def test_garage_data():
    """Test current garage data"""
    print("\n=== Testing Current Garage Data ===")
    
    try:
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        for i, car_data in enumerate(garage_data):
            car_name = car_data.get("name", "unknown")
            parts = car_data.get("parts", {})
            
            print(f"\nCar {i}: {car_name}")
            print(f"  Weight: {car_data.get('weight', 'unknown')} kg")
            print(f"  Value: ${car_data.get('value', 'unknown')}")
            
            # Validate parts
            is_valid, missing_parts, validation_errors = parts_requirements.validate_car_parts(car_data)
            
            if is_valid:
                print("  ✅ All required parts present")
            else:
                print(f"  ❌ Missing parts: {missing_parts}")
                print(f"  ❌ Validation errors: {validation_errors}")
            
            # Show installed parts
            for part_category, part_data in parts.items():
                if part_data is not None:
                    part_name = part_data.get("name", "Unknown")
                    print(f"    {part_category}: {part_name}")
                else:
                    print(f"    {part_category}: None")
                    
    except Exception as e:
        print(f"Error loading garage data: {e}")

def test_save_and_load():
    """Test saving and loading parts"""
    print("\n=== Testing Save and Load Functionality ===")
    
    try:
        # Load current garage data
        with open('data/garage.json', 'r') as f:
            original_garage = json.load(f)
        
        if len(original_garage) > 0:
            car_index = 0
            original_car = original_garage[car_index].copy()
            
            print(f"Original car parts:")
            for category, part in original_car.get("parts", {}).items():
                if part:
                    print(f"  {category}: {part.get('name', 'Unknown')}")
                else:
                    print(f"  {category}: None")
            
            # Try to save the same parts (should work)
            success = save_car_parts(car_index, original_car["parts"])
            
            if success:
                print("✅ Parts saved successfully")
                
                # Load and verify
                with open('data/garage.json', 'r') as f:
                    updated_garage = json.load(f)
                
                updated_car = updated_garage[car_index]
                
                print(f"Updated car parts:")
                for category, part in updated_car.get("parts", {}).items():
                    if part:
                        print(f"  {category}: {part.get('name', 'Unknown')}")
                    else:
                        print(f"  {category}: None")
                
                # Compare
                if original_car["parts"] == updated_car["parts"]:
                    print("✅ Parts match after save/load")
                else:
                    print("❌ Parts don't match after save/load")
                    
            else:
                print("❌ Failed to save parts")
                
    except Exception as e:
        print(f"Error in save/load test: {e}")

if __name__ == "__main__":
    try:
        test_parts_requirements()
        test_garage_data()
        test_save_and_load()
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
