#!/usr/bin/env python3
"""
Test for countdown functionality in single player
"""

import sys
import os
import pygame
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_countdown_class():
    """Test TimeToStart class functionality"""
    print("Testing TimeToStart class...")
    try:
        pygame.init()
        from ui import TimeToStart
        
        # Create countdown timer
        timer = TimeToStart()
        
        # Test initial state
        if timer.time != 3:
            print(f"FAIL: Initial time should be 3, got {timer.time}")
            return False
        
        # Test time update
        start_time = pygame.time.get_ticks()
        
        # Simulate time passing
        pygame.time.wait(1100)  # Wait 1.1 seconds
        timer.update_time()
        
        if timer.time != 2:
            print(f"FAIL: After 1 second, time should be 2, got {timer.time}")
            return False
        
        # Test countdown completion
        pygame.time.wait(2100)  # Wait another 2.1 seconds
        timer.update_time()
        
        if timer.time != 0:
            print(f"FAIL: After 3+ seconds, time should be 0, got {timer.time}")
            return False
        
        print("PASS: TimeToStart class working")
        return True
    except Exception as e:
        print(f"FAIL: TimeToStart class error: {e}")
        return False

def test_countdown_drawing():
    """Test countdown drawing functionality"""
    print("Testing countdown drawing...")
    try:
        pygame.init()
        from ui import TimeToStart
        
        # Create test screen
        screen = pygame.Surface((800, 600))
        timer = TimeToStart()
        
        # Test drawing with time > 0
        timer.time = 3
        timer.draw(screen, 800, 600)
        
        # Test drawing with time = 0
        timer.time = 0
        timer.draw(screen, 800, 600)
        
        print("PASS: Countdown drawing working")
        return True
    except Exception as e:
        print(f"FAIL: Countdown drawing error: {e}")
        return False

def test_countdown_integration():
    """Test countdown integration in game logic"""
    print("Testing countdown integration...")
    try:
        pygame.init()
        from ui import TimeToStart
        
        timer = TimeToStart()
        
        # Simulate game loop logic
        game_started = False
        
        # Before countdown finishes
        if timer.time > 0:
            # Cars should not move
            player_can_move = False
        else:
            # Cars can move
            player_can_move = True
            game_started = True
        
        if player_can_move:
            print("FAIL: Player should not be able to move during countdown")
            return False
        
        # Simulate countdown finishing
        timer.time = 0
        
        if timer.time <= 0:
            player_can_move = True
            game_started = True
        
        if not player_can_move or not game_started:
            print("FAIL: Player should be able to move after countdown")
            return False
        
        print("PASS: Countdown integration working")
        return True
    except Exception as e:
        print(f"FAIL: Countdown integration error: {e}")
        return False

def test_visual_improvements():
    """Test visual improvements to countdown"""
    print("Testing visual improvements...")
    try:
        pygame.init()
        from ui import TimeToStart
        from utils import load_font
        
        # Test font loading
        font = load_font("arial", 144)
        if not font:
            print("FAIL: Could not load countdown font")
            return False
        
        # Test text rendering
        countdown_text = font.render("3", True, (255, 255, 255))
        if not countdown_text:
            print("FAIL: Could not render countdown text")
            return False
        
        # Test background creation
        screen = pygame.Surface((800, 600))
        overlay = pygame.Surface((100, 100))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        
        if overlay.get_alpha() != 180:
            print("FAIL: Overlay alpha not set correctly")
            return False
        
        print("PASS: Visual improvements working")
        return True
    except Exception as e:
        print(f"FAIL: Visual improvements error: {e}")
        return False

def test_game_countdown_logic():
    """Test the actual game countdown logic from game.py"""
    print("Testing game countdown logic...")
    try:
        pygame.init()
        from ui import TimeToStart
        
        # Simulate the game logic
        timer = TimeToStart()
        
        # Test the condition from game.py
        if timer.time > 0:
            # During countdown - cars should be drawn but not move
            cars_drawn = True
            cars_can_move = False
            countdown_shown = True
        else:
            # After countdown - normal gameplay
            cars_drawn = True
            cars_can_move = True
            countdown_shown = False
        
        # Verify countdown phase
        if not cars_drawn:
            print("FAIL: Cars should be drawn during countdown")
            return False
        
        if cars_can_move:
            print("FAIL: Cars should not move during countdown")
            return False
        
        if not countdown_shown:
            print("FAIL: Countdown should be shown")
            return False
        
        # Simulate countdown end
        timer.time = 0
        
        if timer.time > 0:
            cars_can_move = False
        else:
            cars_can_move = True
        
        if not cars_can_move:
            print("FAIL: Cars should be able to move after countdown")
            return False
        
        print("PASS: Game countdown logic working")
        return True
    except Exception as e:
        print(f"FAIL: Game countdown logic error: {e}")
        return False

def main():
    """Run countdown tests"""
    print("=" * 50)
    print("COUNTDOWN FUNCTIONALITY TEST SUITE")
    print("=" * 50)
    
    tests = [
        test_countdown_class,
        test_countdown_drawing,
        test_countdown_integration,
        test_visual_improvements,
        test_game_countdown_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"FAIL: Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 50)
    print("RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("ALL COUNTDOWN TESTS PASSED!")
        print("Countdown should now work correctly in single player")
    else:
        print(f"{total - passed} TESTS FAILED")
        print("Countdown issues remain")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
