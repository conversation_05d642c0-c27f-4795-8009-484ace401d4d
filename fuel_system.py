import json
import time
from valuation_system import valuation_system
from utils import resource_path

class FuelSystem:
    def __init__(self):
        # Performance optimization: simple cache
        self._cache = {}
        self._cache_timestamp = 0
        self._cache_timeout = 1.0  # Cache for 1 second

        # Base fuel consumption rates (liters per second of racing)
        self.base_consumption_rates = {
            "Economy": 0.05,      # Very efficient
            "Standard": 0.08,     # Normal consumption
            "Performance": 0.12,  # Higher consumption
            "Sports Car": 0.18,   # High performance, high consumption
            "Supercar": 0.25,     # Very high consumption
            "Hypercar": 0.35      # Extreme consumption
        }
        
        # Base fuel tank capacities (liters)
        self.base_tank_capacity = 60  # Standard tank size
        
        # Fuel efficiency modifiers based on parts condition
        self.condition_efficiency_bonus = {
            "excellent": 1.0,     # No penalty
            "good": 1.1,          # 10% more consumption
            "fair": 1.25,         # 25% more consumption
            "poor": 1.5,          # 50% more consumption
            "broken": 2.0         # 100% more consumption
        }
        
        # Performance impact of low fuel
        self.low_fuel_performance_impact = {
            0.9: 1.0,   # 90-100% fuel = no impact
            0.7: 0.98,  # 70-89% fuel = 2% performance loss
            0.5: 0.95,  # 50-69% fuel = 5% performance loss
            0.3: 0.90,  # 30-49% fuel = 10% performance loss
            0.1: 0.80,  # 10-29% fuel = 20% performance loss
            0.0: 0.60   # 0-9% fuel = 40% performance loss
        }
    
    def calculate_fuel_capacity(self, car_data):
        """Calculate total fuel capacity based on car and modifications"""
        base_capacity = self.base_tank_capacity
        
        # Lighter cars might have smaller tanks, heavier cars larger tanks
        weight = car_data.get("weight", 500)
        weight_modifier = 1.0 + (weight - 500) / 1000  # ±50% based on weight difference
        
        return int(base_capacity * weight_modifier)
    
    def calculate_fuel_consumption_rate(self, car_data, usage_data=None):
        """Calculate fuel consumption rate per second of racing"""
        if usage_data is None:
            usage_data = valuation_system.get_default_usage_data()
        
        # Get performance class
        performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
        performance_class = performance_data["performance_class"]
        
        # Base consumption for this performance class
        base_rate = self.base_consumption_rates.get(performance_class, 0.1)
        
        # Modify based on engine condition
        engine_condition = performance_data["condition_effects"]["engine_condition"]
        condition_category = valuation_system.get_condition_category(engine_condition)
        condition_modifier = self.condition_efficiency_bonus.get(condition_category, 1.2)
        
        # Modify based on total horsepower (more power = more fuel)
        hp_modifier = 1.0 + (performance_data["total_horsepower"] - 200) / 1000
        hp_modifier = max(0.5, min(2.0, hp_modifier))  # Clamp between 0.5x and 2.0x
        
        return base_rate * condition_modifier * hp_modifier
    
    def get_performance_impact(self, fuel_percentage):
        """Get performance impact based on current fuel level"""
        for threshold, impact in sorted(self.low_fuel_performance_impact.items(), reverse=True):
            if fuel_percentage >= threshold:
                return impact
        return 0.6  # Minimum performance at 0% fuel
    
    def initialize_fuel_data(self, car_index):
        """Initialize fuel data for a car if not exists"""
        try:
            with open(resource_path('data/profile.json'), 'r') as f:
                profile_data = json.load(f)
            
            # Initialize fuel data structure if not exists
            if "fuel_data" not in profile_data:
                profile_data["fuel_data"] = {"cars": {}}
            
            if "cars" not in profile_data["fuel_data"]:
                profile_data["fuel_data"]["cars"] = {}
            
            car_key = str(car_index)
            if car_key not in profile_data["fuel_data"]["cars"]:
                # Get car data to calculate capacity
                with open(resource_path('data/garage.json'), 'r') as f:
                    garage_data = json.load(f)
                
                if car_index < len(garage_data):
                    car_data = garage_data[car_index]
                    fuel_capacity = self.calculate_fuel_capacity(car_data)
                    
                    profile_data["fuel_data"]["cars"][car_key] = {
                        "current_fuel": fuel_capacity,  # Start with full tank
                        "max_capacity": fuel_capacity,
                        "last_refuel": int(time.time())
                    }
                    
                    # Save updated profile
                    with open(resource_path('data/profile.json'), 'w') as f:
                        json.dump(profile_data, f, indent=4)
            
            return True
        except Exception as e:
            print(f"Error initializing fuel data: {e}")
            return False
    
    def get_fuel_data(self, car_index):
        """Get current fuel data for a car (with caching for performance)"""
        try:
            current_time = time.time()
            cache_key = f"fuel_{car_index}"

            # Check cache first
            if (cache_key in self._cache and
                current_time - self._cache_timestamp < self._cache_timeout):
                return self._cache[cache_key]

            with open(resource_path('data/profile.json'), 'r') as f:
                profile_data = json.load(f)

            car_key = str(car_index)
            fuel_data = profile_data.get("fuel_data", {}).get("cars", {}).get(car_key, {})

            if not fuel_data:
                # Initialize if not exists
                if self.initialize_fuel_data(car_index):
                    # Re-read the data after initialization
                    with open(resource_path('data/profile.json'), 'r') as f:
                        profile_data = json.load(f)
                    fuel_data = profile_data.get("fuel_data", {}).get("cars", {}).get(car_key, {})
                    if fuel_data:
                        # Cache the result
                        self._cache[cache_key] = fuel_data
                        self._cache_timestamp = current_time
                        return fuel_data
                # If initialization failed, return default
                default_data = {"current_fuel": 60, "max_capacity": 60, "last_refuel": int(time.time())}
                self._cache[cache_key] = default_data
                self._cache_timestamp = current_time
                return default_data

            # Cache the result
            self._cache[cache_key] = fuel_data
            self._cache_timestamp = current_time
            return fuel_data
        except Exception as e:
            print(f"Error getting fuel data: {e}")
            return {"current_fuel": 60, "max_capacity": 60, "last_refuel": int(time.time())}

    def _clear_cache(self):
        """Clear cache when data changes"""
        self._cache.clear()
        self._cache_timestamp = 0

    def consume_fuel(self, car_index, race_time_seconds, car_data, usage_data=None):
        """Consume fuel during a race"""
        try:
            fuel_data = self.get_fuel_data(car_index)
            consumption_rate = self.calculate_fuel_consumption_rate(car_data, usage_data)
            
            fuel_consumed = consumption_rate * race_time_seconds
            new_fuel_level = max(0, fuel_data["current_fuel"] - fuel_consumed)
            
            # Update fuel data
            with open(resource_path('data/profile.json'), 'r') as f:
                profile_data = json.load(f)
            
            car_key = str(car_index)
            profile_data["fuel_data"]["cars"][car_key]["current_fuel"] = new_fuel_level
            
            with open(resource_path('data/profile.json'), 'w') as f:
                json.dump(profile_data, f, indent=4)

            # Clear cache since data changed
            self._clear_cache()

            # Auto-save if current save slot is set
            from save_system import save_system
            if save_system.current_save_slot:
                save_system.save_game(save_system.current_save_slot)
            
            return {
                "fuel_consumed": fuel_consumed,
                "remaining_fuel": new_fuel_level,
                "fuel_percentage": new_fuel_level / fuel_data["max_capacity"],
                "performance_impact": self.get_performance_impact(new_fuel_level / fuel_data["max_capacity"])
            }
            
        except Exception as e:
            print(f"Error consuming fuel: {e}")
            return {"fuel_consumed": 0, "remaining_fuel": 60, "fuel_percentage": 1.0, "performance_impact": 1.0}
    
    def refuel_car(self, car_index, fuel_amount=None):
        """Refuel a car (full tank if no amount specified)"""
        try:
            fuel_data = self.get_fuel_data(car_index)
            
            if fuel_amount is None:
                # Full refuel
                fuel_to_add = fuel_data["max_capacity"] - fuel_data["current_fuel"]
            else:
                # Partial refuel
                fuel_to_add = min(fuel_amount, fuel_data["max_capacity"] - fuel_data["current_fuel"])
            
            # Calculate refuel cost (1$ per liter)
            refuel_cost = int(fuel_to_add * 1.0)
            
            # Check if player has enough money
            with open(resource_path('data/profile.json'), 'r') as f:
                profile_data = json.load(f)
            
            if profile_data["money"] < refuel_cost:
                return False, f"Nie masz wystarczająco pieniędzy. Potrzebujesz {refuel_cost}$"
            
            # Deduct money and add fuel
            profile_data["money"] -= refuel_cost
            car_key = str(car_index)
            profile_data["fuel_data"]["cars"][car_key]["current_fuel"] += fuel_to_add
            profile_data["fuel_data"]["cars"][car_key]["last_refuel"] = int(time.time())
            
            with open(resource_path('data/profile.json'), 'w') as f:
                json.dump(profile_data, f, indent=4)

            # Clear cache since data changed
            self._clear_cache()

            # Auto-save if current save slot is set
            from save_system import save_system
            if save_system.current_save_slot:
                save_system.save_game(save_system.current_save_slot)

            return True, f"Zatankowano {fuel_to_add:.1f}L za {refuel_cost}$"
            
        except Exception as e:
            print(f"Error refueling: {e}")
            return False, "Błąd podczas tankowania"

# Global fuel system instance
fuel_system = FuelSystem()
