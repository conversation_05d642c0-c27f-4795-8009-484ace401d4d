"""
Car Compatibility System - Defines which parts are compatible with which car models
"""

class CarCompatibility:
    def __init__(self):
        # Define compatibility rules for each car model
        self.compatibility_rules = {
            "old": {
                "description": "Klasyczny samochód z ograniczoną kompatybilnością części",
                "engine": [
                    "Basic I4",      # Domyślny silnik dla old
                    "Tuned I4"
                ],
                "turbo": [
                    # Brak turbo dla old - klasyczne podejście
                ],
                "intercooler": [
                    # Brak intercoolera dla old - nie ma turbo
                ],
                "ecu": [
                    "Stage 1 Tune"   # Domyślny ECU dla old
                ]
            },
            "future": {
                "description": "Futurystyczny samochód z umiarkowaną kompatybilnością części",
                "engine": [
                    "Basic I4",
                    "Tuned I4",
                    "V6 Naturally Aspirated",  # Domyślny silnik dla future
                    "V6 Twin Turbo"
                ],
                "turbo": [
                    "Small Turbo",
                    "Medium Turbo",            # <PERSON><PERSON><PERSON>lny turbo dla future
                    "Large Turbo"
                ],
                "intercooler": [
                    "Basic Front Mount",
                    "Large Front Mount",       # Domyślny intercooler dla future
                    "Top Mount"
                ],
                "ecu": [
                    "Stage 1 Tune",
                    "Stage 2 Tune",           # Domyślny ECU dla future
                    "Stage 3 Tune"
                ]
            },
            "Xtreme": {
                "description": "Najlepszy samochód - kompatybilny ze wszystkimi częściami",
                "engine": [
                    "Basic I4",
                    "Tuned I4",
                    "V6 Naturally Aspirated",
                    "V6 Twin Turbo", 
                    "V8 Muscle",
                    "V8 High Performance",
                    "V10 Supercar",
                    "V12 Hypercar"
                ],
                "turbo": [
                    "Small Turbo",
                    "Medium Turbo",
                    "Large Turbo",
                    "Twin Turbo Setup",        # Domyślny turbo dla Xtreme
                    "Ball Bearing Turbo",
                    "Variable Geometry Turbo"
                ],
                "intercooler": [
                    "Basic Front Mount",
                    "Large Front Mount",
                    "Top Mount",
                    "Water-to-Air",
                    "Race Spec Intercooler"    # Domyślny intercooler dla Xtreme
                ],
                "ecu": [
                    "Stage 1 Tune",
                    "Stage 2 Tune",
                    "Stage 3 Tune",
                    "Custom Dyno Tune",
                    "Race ECU",               # Domyślny ECU dla Xtreme
                    "Pro Tuner ECU"
                ]
            }
        }
        
        # Define car progression order
        self.progression_order = ["old", "future", "Xtreme"]
        
        # Define car specifications
        self.car_specs = {
            "old": {
                "weight": 750,  # Najcięższy - klasyk
                "value": 800,   # Najtańszy
                "description": "Klasyczny samochód z podstawowym silnikiem I4, ciężki i z ograniczonymi możliwościami tuningu"
            },
            "future": {
                "weight": 520,  # Średni - nowoczesne materiały
                "value": 4500,  # Średnia cena
                "description": "Futurystyczny samochód z silnikiem V6 i turbo, dobry balans mocy i wagi"
            },
            "Xtreme": {
                "weight": 380,  # Najlżejszy - zaawansowane materiały
                "value": 12000, # Najdroższy
                "description": "Najlepszy samochód w grze - lekki, mocny V8 z twin turbo i kompatybilny ze wszystkimi częściami"
            }
        }
    
    def is_part_compatible(self, car_name, part_name, part_category):
        """
        Check if a specific part is compatible with a car
        Returns: (is_compatible, reason)
        """
        if car_name not in self.compatibility_rules:
            return False, f"Unknown car model: {car_name}"
        
        if part_category not in self.compatibility_rules[car_name]:
            return False, f"Invalid part category: {part_category}"
        
        compatible_parts = self.compatibility_rules[car_name][part_category]
        
        if part_name in compatible_parts:
            return True, "Part is compatible"
        else:
            return False, f"Part '{part_name}' is not compatible with {car_name}. Compatible parts: {', '.join(compatible_parts) if compatible_parts else 'None'}"
    
    def get_compatible_parts(self, car_name, part_category):
        """
        Get list of compatible parts for a car and category
        Returns: list of compatible part names
        """
        if car_name not in self.compatibility_rules:
            return []
        
        if part_category not in self.compatibility_rules[car_name]:
            return []
        
        return self.compatibility_rules[car_name][part_category].copy()
    
    def get_car_description(self, car_name):
        """Get description of a car model"""
        if car_name in self.car_specs:
            return self.car_specs[car_name]["description"]
        return "Unknown car model"
    
    def get_car_specs(self, car_name):
        """Get specifications of a car model"""
        if car_name in self.car_specs:
            return self.car_specs[car_name].copy()
        return None
    
    def get_progression_level(self, car_name):
        """Get progression level of a car (0 = lowest, higher = better)"""
        try:
            return self.progression_order.index(car_name)
        except ValueError:
            return -1
    
    def get_next_car_in_progression(self, current_car):
        """Get the next car in progression sequence"""
        current_level = self.get_progression_level(current_car)
        if current_level >= 0 and current_level < len(self.progression_order) - 1:
            return self.progression_order[current_level + 1]
        return None
    
    def get_all_car_models(self):
        """Get all available car models in progression order"""
        return self.progression_order.copy()
    
    def validate_car_setup(self, car_name, parts):
        """
        Validate entire car setup for compatibility
        Returns: (is_valid, errors, warnings)
        """
        errors = []
        warnings = []
        
        if car_name not in self.compatibility_rules:
            errors.append(f"Unknown car model: {car_name}")
            return False, errors, warnings
        
        # Check each installed part
        for part_category, part_data in parts.items():
            if part_data is not None:
                part_name = part_data.get("name", "Unknown")
                is_compatible, reason = self.is_part_compatible(car_name, part_name, part_category)
                
                if not is_compatible:
                    errors.append(f"{part_category}: {reason}")
        
        # Check for missing required parts based on car type
        if car_name == "old":
            # Old car requires engine and ECU, no turbo/intercooler
            if not parts.get("engine"):
                errors.append("Engine is required for old car")
            if not parts.get("ecu"):
                errors.append("ECU is required for old car")
            if parts.get("turbo"):
                warnings.append("Turbo is not compatible with old car")
            if parts.get("intercooler"):
                warnings.append("Intercooler is not needed without turbo")
        
        elif car_name == "future":
            # Future car requires all parts
            required_parts = ["engine", "ecu", "intercooler"]
            for part_cat in required_parts:
                if not parts.get(part_cat):
                    errors.append(f"{part_cat} is required for future car")
        
        elif car_name == "Xtreme":
            # Xtreme car requires all parts
            required_parts = ["engine", "ecu", "intercooler"]
            for part_cat in required_parts:
                if not parts.get(part_cat):
                    errors.append(f"{part_cat} is required for Xtreme car")
        
        is_valid = len(errors) == 0
        return is_valid, errors, warnings

# Global compatibility system instance
car_compatibility = CarCompatibility()
