import pygame
from utils import load_image
class Background:
    def __init__(self, name, screen_width, screen_height):
        self.x_cord = 0
        self.y_cord = 0
        self.img = load_image(f'./assets/img/{name}.png')
        self.height = self.img.get_height() * screen_height // self.img.get_height()
        self.width = self.img.get_width() * screen_width // self.img.get_width()
        self.img = pygame.transform.scale(self.img, (self.width, self.height))

    def draw(self, screen):
        screen.blit(self.img, (self.x_cord, self.y_cord))