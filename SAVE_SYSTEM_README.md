# 🎮 System Zapisów Gry - Xtreme Cars

## 📋 Przegląd

System zapisów umożliwia graczom zapisywanie i wczytywanie stanu gry w maksymalnie 3 slotach. System automatycznie zapisuje postęp po ważnych akcjach i pozwala na ręczne zarządzanie zapisami.

## 🚀 Funkcje

### ✅ **Podstawowe Funkcje**
- **3 sloty zapisu** - maksymalnie 3 niezależne zapisy gry
- **Automatyczne zapisywanie** - po wy<PERSON>ch, zakupach, zmianach w garaży
- **Ręczne zapisywanie** - przez menu główne
- **Wczytywanie zapisów** - przywracanie poprzedniego stanu gry
- **Usuwanie zapisów** - zarządzanie slotami zapisu
- **Nowa gra** - tworzenie świeżego profilu

### 📊 **Informacje o Zapisach**
Każdy zapis zawiera:
- **Nazwa gracza**
- **Poziom** 
- **Pieniądze**
- **Data i czas zapisu**
- **Czas gry** (w formacie HH:MM)
- **Kompletny stan gry** (profil + garaż)

## 🎯 **Jak Używać**

### **Menu Główne**
1. **"Wczytaj Grę"** - otwiera menu wczytywania
2. **"Zapisz Grę"** - otwiera menu zapisywania
3. **"Nowa Gra"** - tworzy nowy profil gracza

### **Menu Wczytywania**
- **Klik na slot** - wczytuje grę z wybranego slotu
- **Prawy klik** - usuwa zapis ze slotu
- **"Nowa Gra"** - rozpoczyna od początku
- **"Powrót"** - wraca do menu głównego

### **Menu Zapisywania**
- **Klik na pusty slot** - zapisuje grę w nowym slocie
- **Klik na zajęty slot** - nadpisuje istniejący zapis
- **"Powrót"** - wraca do menu głównego

## 🔄 **Automatyczne Zapisywanie**

System automatycznie zapisuje grę po:
- **Zakończeniu wyścigu** (wygrana/przegrana)
- **Zakupie w sklepie** (samochody/części)
- **Zmianie części w garaży**
- **Zmianie koloru samochodu**
- **Zmianie nazwy gracza**
- **Wyborze samochodu**

## 📁 **Struktura Plików**

```
data/saves/
├── slot_1/
│   ├── profile.json    # Stan gracza
│   ├── garage.json     # Samochody i części
│   └── metadata.json   # Informacje o zapisie
├── slot_2/
│   └── ...
└── slot_3/
    └── ...
```

### **metadata.json**
```json
{
    "username": "Nazwa Gracza",
    "level": 5,
    "money": 15000,
    "save_date": "2024-01-15 14:30:25",
    "playtime": "2:45",
    "version": "1.0"
}
```

## 🛠 **Implementacja Techniczna**

### **Główne Klasy**
- **`SaveSystem`** - główna logika zapisów
- **`SaveSlotCard`** - interfejs slotu zapisu
- **`ConfirmDialog`** - dialogi potwierdzenia
- **`draw_save_menu()`** - interfejs menu zapisów

### **Kluczowe Metody**
```python
# Zapisywanie gry
save_system.save_game(slot_number, playtime_seconds)

# Wczytywanie gry
save_system.load_game(slot_number)

# Usuwanie zapisu
save_system.delete_save(slot_number)

# Nowa gra
save_system.create_new_game()

# Informacje o slotach
save_system.get_save_slots()
```

## 🔒 **Bezpieczeństwo**

- **Walidacja slotów** - sprawdzanie poprawności numerów slotów (1-3)
- **Obsługa błędów** - graceful handling niepoprawnych plików
- **Backup danych** - zachowanie oryginalnych plików przy błędach
- **Atomowe operacje** - zapisy są kompletne lub w ogóle nie wykonane

## 🎨 **Interfejs Użytkownika**

### **Kolory i Style**
- **Pusty slot**: Ciemnoszary (40, 40, 40)
- **Zajęty slot**: Szary (80, 80, 80)  
- **Hover**: Jasnoszary (100, 100, 100)
- **Ramki**: Białe (200, 200, 200)

### **Informacje Wyświetlane**
- **Numer slotu** (duża czcionka)
- **Nazwa gracza**
- **Poziom i pieniądze**
- **Data zapisu**
- **Czas gry**

## 🚨 **Rozwiązywanie Problemów**

### **Częste Problemy**
1. **Brak katalogu saves** - automatycznie tworzony przy pierwszym użyciu
2. **Uszkodzone pliki zapisu** - slot oznaczany jako pusty
3. **Brak uprawnień** - sprawdź uprawnienia do zapisu w katalogu data/

### **Debugging**
```python
# Sprawdzenie stanu systemu zapisów
print(f"Aktualny slot: {save_system.current_save_slot}")
print(f"Dostępne sloty: {save_system.get_save_slots()}")
```

## 📈 **Przyszłe Rozszerzenia**

### **Planowane Funkcje**
- **Eksport/Import zapisów** - udostępnianie zapisów między graczami
- **Kompresja zapisów** - zmniejszenie rozmiaru plików
- **Więcej slotów** - opcjonalnie więcej niż 3 sloty
- **Automatyczne backupy** - okresowe kopie zapasowe
- **Statystyki gry** - szczegółowe informacje o postępie

### **Możliwe Ulepszenia**
- **Miniaturki samochodów** w slotach zapisu
- **Sortowanie zapisów** według daty/poziomu
- **Wyszukiwanie zapisów** według nazwy gracza
- **Kategorie zapisów** (np. przed ważnymi wyścigami)

## 🎯 **Podsumowanie**

System zapisów znacznie poprawia doświadczenie gracza poprzez:
- **Bezpieczeństwo postępu** - brak strachu przed utratą danych
- **Eksperymentowanie** - możliwość powrotu do poprzednich stanów
- **Wielokrotne profile** - różni gracze na tym samym komputerze
- **Wygoda użytkowania** - automatyczne i ręczne opcje zapisu

System jest w pełni funkcjonalny i gotowy do użycia! 🎉
