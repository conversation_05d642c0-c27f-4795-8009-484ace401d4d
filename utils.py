# utils.py

import sys
import os
import pygame

def get_user_data_folder():
    """Get the user data folder for storing writable game data"""
    appdata = os.getenv('APPDATA')
    user_data_folder = os.path.join(appdata, "Xtra Cars", "GameData")
    os.makedirs(user_data_folder, exist_ok=True)
    return user_data_folder

def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    # Check if this is a user data file that needs to be writable
    user_data_files = ['data/profile.json', 'data/garage.json']

    if relative_path in user_data_files:
        # For user data files, use the user data folder
        user_data_folder = get_user_data_folder()
        filename = os.path.basename(relative_path)
        user_file_path = os.path.join(user_data_folder, filename)

        # If the file doesn't exist in user folder, copy it from the installation folder
        if not os.path.exists(user_file_path):
            # Get the original file from installation folder
            if hasattr(sys, '_MEIPASS'):
                original_path = os.path.join(sys._MEIPASS, relative_path)
            else:
                original_path = os.path.join(os.path.abspath("."), relative_path)

            # Copy the original file to user folder if it exists
            if os.path.exists(original_path):
                import shutil
                shutil.copy2(original_path, user_file_path)

        return user_file_path

    # For read-only resources (images, sounds, etc.), use the original logic
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)

def load_image(path):
    """Load an image using pygame with resource_path."""
    return pygame.image.load(resource_path(path))

def load_sound(path):
    """Load a sound using pygame with resource_path."""
    return pygame.mixer.Sound(resource_path(path))

def load_font(path, size):
    """Load a font file (.ttf) or system font using pygame. Always prefer system font for Polish support."""
    # Always use system font for best Unicode/Polish support
    # If path is a font file, fallback to system font if loading fails
    try:
        if path.lower().endswith(('.ttf', '.otf')):
            # Try loading TTF, but fallback to system font if error
            try:
                return pygame.font.Font(resource_path(path), size)
            except Exception:
                pass
        # Use system font (Arial or DejaVu Sans for Polish)
        return pygame.font.SysFont("DejaVu Sans", size) or pygame.font.SysFont("Arial", size)
    except Exception:
        # As a last resort, use default font
        return pygame.font.SysFont(None, size)
