import pygame
pygame.init()
pygame.font.init()
pygame.mixer.init()
from game import Game
from car_index_validator import CarIndexValidator

def main():
    # Validate and fix car indices before starting the game
    print("Validating car indices...")
    success, message, changes = CarIndexValidator.validate_and_fix_car_indices()

    if not success:
        print(f"❌ Car index validation failed: {message}")
        return

    if changes:
        print("🔧 Car index issues found and fixed:")
        for change in changes:
            print(f"  - {change}")
    else:
        print("✅ Car indices are valid")

    game = Game()
    game.run()

if __name__ == '__main__':
    main()