#!/usr/bin/env python3
"""
Test script to verify single player fixes:
1. Opponent parts display correctly
2. Opponent doesn't accelerate during countdown
"""

import json
import pygame
import time
from utils import resource_path
from level_info import draw_level_info_screen
from cars import Opponent<PERSON>ar

def test_opponent_data_loading():
    """Test that opponent data loads correctly and shows proper part names"""
    print("=== Testing Opponent Data Loading ===")
    
    try:
        # Test loading opponent data with resource_path
        with open(resource_path('data/oponent_levels.json'), 'r', encoding='utf-8') as f:
            opponents_data = json.load(f)
        
        if not opponents_data:
            print("❌ No opponent data loaded")
            return False
        
        print(f"✅ Loaded {len(opponents_data)} opponents")
        
        # Test first few opponents for proper part names
        for i, opponent in enumerate(opponents_data[:3]):
            level = i + 1
            print(f"\n--- Level {level} Opponent ---")
            print(f"Name: {opponent.get('opponent_name', 'Unknown')}")
            
            parts = opponent.get('parts', {})
            for part_type, part in parts.items():
                if part:
                    part_name = part.get('name', 'MISSING NAME')
                    if part_name == 'MISSING NAME':
                        print(f"❌ {part_type}: Missing part name")
                        return False
                    else:
                        print(f"✅ {part_type}: {part_name}")
                else:
                    print(f"⚪ {part_type}: None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading opponent data: {e}")
        return False

def test_opponent_speed_during_countdown():
    """Test that opponent doesn't accelerate during countdown"""
    print("\n=== Testing Opponent Speed During Countdown ===")
    
    try:
        # Initialize pygame for testing
        pygame.init()
        
        # Create a test opponent
        test_parts = {
            "engine": {
                "name": "Test Engine",
                "horsepower": 200,
                "weight": 180
            },
            "turbo": None,
            "intercooler": None,
            "ecu": None
        }
        
        opponent = OpponentCar(
            name="test_car",
            color="0",
            weight=500,
            parts=test_parts,
            x_cord=100,
            y_cord=200,
            start_time=time.time(),
            opponent_name="Test Opponent"
        )
        
        print(f"Initial speed: {opponent.speed}")
        print(f"Max speed: {opponent.max_speed}")
        print(f"Race started: {opponent.race_started}")
        
        # Simulate countdown updates (dt = 0)
        print("\n--- Simulating countdown (dt=0) ---")
        for i in range(5):
            initial_speed = opponent.speed
            opponent.update(0)  # dt = 0 during countdown
            print(f"Update {i+1}: Speed {initial_speed} -> {opponent.speed}")
            
            if opponent.speed > 0:
                print("❌ Opponent accelerated during countdown!")
                return False
        
        print("✅ Opponent stayed at 0 speed during countdown")
        
        # Simulate race start (dt > 0)
        print("\n--- Simulating race start (dt>0) ---")
        for i in range(10):
            initial_speed = opponent.speed
            opponent.update(0.016)  # Normal frame time
            print(f"Race update {i+1}: Speed {initial_speed:.1f} -> {opponent.speed:.1f}")
            
            if i == 0 and opponent.speed <= 0:
                print("❌ Opponent didn't start accelerating after race start!")
                return False
        
        print("✅ Opponent accelerated properly after race start")
        
        # Test that race_started flag works correctly
        if not opponent.race_started:
            print("❌ race_started flag not set correctly")
            return False
        
        print("✅ race_started flag working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing opponent speed: {e}")
        return False
    finally:
        pygame.quit()

def test_level_info_screen_data():
    """Test that level info screen can load and display opponent data correctly"""
    print("\n=== Testing Level Info Screen Data ===")
    
    try:
        # Test loading data for level info screen
        with open(resource_path('data/oponent_levels.json'), 'r', encoding='utf-8') as f:
            opponents_data = json.load(f)
        
        # Test first opponent
        if len(opponents_data) > 0:
            opponent = opponents_data[0]
            
            # Check that all required fields exist
            required_fields = ['opponent_name', 'parts']
            for field in required_fields:
                if field not in opponent:
                    print(f"❌ Missing required field: {field}")
                    return False
            
            # Check engine part specifically
            engine = opponent.get('parts', {}).get('engine', {})
            if not engine:
                print("❌ No engine part found")
                return False
            
            engine_name = engine.get('name', '')
            if not engine_name or engine_name == 'Nieznana część':
                print(f"❌ Invalid engine name: '{engine_name}'")
                return False
            
            print(f"✅ Engine name: {engine_name}")
            
            # Check other parts
            parts = opponent.get('parts', {})
            for part_type, part in parts.items():
                if part:
                    part_name = part.get('name', '')
                    if part_name:
                        print(f"✅ {part_type}: {part_name}")
                    else:
                        print(f"❌ {part_type}: Missing name")
                        return False
                else:
                    print(f"⚪ {part_type}: None (OK)")
        
        print("✅ Level info screen data validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing level info screen data: {e}")
        return False

def test_opponent_speed_consistency():
    """Test that opponent speed behavior is consistent and predictable"""
    print("\n=== Testing Opponent Speed Consistency ===")
    
    try:
        pygame.init()
        
        # Create opponent
        test_parts = {
            "engine": {"name": "Test Engine", "horsepower": 150, "weight": 180},
            "turbo": None,
            "intercooler": None,
            "ecu": None
        }
        
        opponent = OpponentCar("test", "0", 500, test_parts, 100, 200, time.time(), "Test")
        
        # Test multiple countdown cycles
        print("Testing multiple countdown cycles...")
        for cycle in range(3):
            print(f"\n--- Countdown Cycle {cycle + 1} ---")
            
            # Reset opponent
            opponent.speed = 0
            opponent.race_started = False
            opponent.frame_count = 0
            
            # Countdown phase
            for i in range(5):
                opponent.update(0)
                if opponent.speed > 0:
                    print(f"❌ Speed > 0 during countdown cycle {cycle + 1}")
                    return False
            
            print(f"✅ Countdown cycle {cycle + 1}: Speed stayed at 0")
            
            # Race phase
            speeds = []
            for i in range(10):
                opponent.update(0.016)
                speeds.append(opponent.speed)
            
            # Check that speed increases during race
            if speeds[-1] <= speeds[0]:
                print(f"❌ Speed didn't increase during race cycle {cycle + 1}")
                return False
            
            print(f"✅ Race cycle {cycle + 1}: Speed increased from {speeds[0]:.1f} to {speeds[-1]:.1f}")
        
        print("✅ Opponent speed consistency test passed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing speed consistency: {e}")
        return False
    finally:
        pygame.quit()

def main():
    """Run all single player fix tests"""
    print("Single Player Fixes Test Suite")
    print("=" * 50)
    
    tests = [
        test_opponent_data_loading,
        test_opponent_speed_during_countdown,
        test_level_info_screen_data,
        test_opponent_speed_consistency
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All single player fixes working correctly!")
        print("\nFixed issues:")
        print("1. ✅ Opponent parts display correctly (no more 'nieznana część')")
        print("2. ✅ Opponent doesn't accelerate during countdown")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
