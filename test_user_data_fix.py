#!/usr/bin/env python3
"""
Test script to verify user data folder fix for permission issues
"""

import os
import json
import sys
import tempfile
import shutil
from utils import resource_path, get_user_data_folder

def test_user_data_folder_creation():
    """Test that user data folder is created correctly"""
    print("=== Testing User Data Folder Creation ===")
    
    try:
        user_data_folder = get_user_data_folder()
        print(f"User data folder: {user_data_folder}")
        
        # Check if folder exists
        if not os.path.exists(user_data_folder):
            print("❌ User data folder was not created")
            return False
        
        # Check if we can write to it
        test_file = os.path.join(user_data_folder, "test_write.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        # Clean up
        os.remove(test_file)
        
        print("✅ User data folder created and writable")
        return True
        
    except Exception as e:
        print(f"❌ Error testing user data folder: {e}")
        return False

def test_profile_file_access():
    """Test that profile.json is accessible through resource_path"""
    print("\n=== Testing Profile File Access ===")
    
    try:
        # Test reading profile
        profile_path = resource_path('data/profile.json')
        print(f"Profile path: {profile_path}")
        
        with open(profile_path, 'r') as f:
            profile_data = json.load(f)
        
        print(f"✅ Profile loaded successfully")
        print(f"   Username: {profile_data.get('username', 'Unknown')}")
        print(f"   Money: {profile_data.get('money', 0)}")
        
        # Test writing to profile (backup first)
        backup_data = profile_data.copy()
        
        # Make a small change
        original_money = profile_data.get('money', 0)
        profile_data['money'] = original_money + 1
        
        with open(profile_path, 'w') as f:
            json.dump(profile_data, f, indent=4)
        
        print("✅ Profile write test successful")
        
        # Restore original data
        with open(profile_path, 'w') as f:
            json.dump(backup_data, f, indent=4)
        
        print("✅ Profile restored to original state")
        return True
        
    except Exception as e:
        print(f"❌ Error testing profile file access: {e}")
        return False

def test_garage_file_access():
    """Test that garage.json is accessible through resource_path"""
    print("\n=== Testing Garage File Access ===")
    
    try:
        # Test reading garage
        garage_path = resource_path('data/garage.json')
        print(f"Garage path: {garage_path}")
        
        with open(garage_path, 'r') as f:
            garage_data = json.load(f)
        
        print(f"✅ Garage loaded successfully")
        print(f"   Number of cars: {len(garage_data)}")
        
        if garage_data:
            print(f"   First car: {garage_data[0].get('name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing garage file access: {e}")
        return False

def test_file_copying_mechanism():
    """Test that files are copied from installation to user folder when needed"""
    print("\n=== Testing File Copying Mechanism ===")
    
    try:
        user_data_folder = get_user_data_folder()
        
        # Check if profile.json exists in user folder
        user_profile_path = os.path.join(user_data_folder, "profile.json")
        user_garage_path = os.path.join(user_data_folder, "garage.json")
        
        print(f"User profile exists: {os.path.exists(user_profile_path)}")
        print(f"User garage exists: {os.path.exists(user_garage_path)}")
        
        # Test resource_path function
        resource_profile_path = resource_path('data/profile.json')
        resource_garage_path = resource_path('data/garage.json')
        
        print(f"Resource profile path: {resource_profile_path}")
        print(f"Resource garage path: {resource_garage_path}")
        
        # Verify they point to user folder
        if user_profile_path in resource_profile_path:
            print("✅ Profile correctly redirected to user folder")
        else:
            print("❌ Profile not redirected to user folder")
            return False
            
        if user_garage_path in resource_garage_path:
            print("✅ Garage correctly redirected to user folder")
        else:
            print("❌ Garage not redirected to user folder")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing file copying mechanism: {e}")
        return False

def test_permission_simulation():
    """Simulate the permission error scenario"""
    print("\n=== Testing Permission Error Scenario ===")
    
    try:
        # This test simulates what would happen in Program Files
        print("Simulating installation in read-only directory...")
        
        # The resource_path function should automatically redirect
        # user data files to the writable user folder
        profile_path = resource_path('data/profile.json')
        
        # Check if the path is in a writable location
        user_data_folder = get_user_data_folder()
        
        if user_data_folder.lower() in profile_path.lower():
            print("✅ User data files correctly redirected to writable location")
            print(f"   Writable path: {profile_path}")
            return True
        else:
            print("❌ User data files not redirected to writable location")
            print(f"   Current path: {profile_path}")
            return False
        
    except Exception as e:
        print(f"❌ Error in permission simulation test: {e}")
        return False

def main():
    """Run all tests"""
    print("User Data Folder Fix Test Suite")
    print("=" * 50)
    
    tests = [
        test_user_data_folder_creation,
        test_profile_file_access,
        test_garage_file_access,
        test_file_copying_mechanism,
        test_permission_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! User data fix should work correctly.")
        print("\nThe game should now work properly when installed in Program Files.")
        print("User data will be stored in: %APPDATA%\\Xtra Cars\\GameData")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
