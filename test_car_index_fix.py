#!/usr/bin/env python3
"""
Test script to verify the car index management fix
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from garage_screen import fix_car_data_consistency

def test_car_index_fix():
    """Test the car index management fix"""
    print("=== Testing Car Index Management Fix ===")
    
    try:
        # Load current data
        with open('data/profile.json', 'r') as f:
            profile_data = json.load(f)
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        print(f"Before fix:")
        print(f"  Profile owned_cars: {profile_data.get('inventory', {}).get('owned_cars', [])}")
        print(f"  Profile selected_car: {profile_data.get('cars', {}).get('selected_car', 'N/A')}")
        print(f"  Garage entries: {len(garage_data)}")
        print(f"  Garage car names: {[car.get('name', 'Unknown') for car in garage_data]}")
        
        # Check for inconsistencies
        owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
        selected_car = profile_data.get("cars", {}).get("selected_car", 0)
        
        inconsistency_detected = False
        
        if len(owned_cars) != len(garage_data):
            print(f"  ❌ INCONSISTENCY: {len(owned_cars)} owned cars vs {len(garage_data)} garage entries")
            inconsistency_detected = True
        
        if selected_car >= len(garage_data):
            print(f"  ❌ INCONSISTENCY: selected_car index {selected_car} >= garage entries {len(garage_data)}")
            inconsistency_detected = True
        
        if not inconsistency_detected:
            print("  ✅ No inconsistencies detected")
            return True
        
        # Apply fix
        print("\nApplying fix...")
        fix_result = fix_car_data_consistency()
        
        if fix_result:
            print("  ✅ Fix applied successfully")
            
            # Reload data to verify fix
            with open('data/profile.json', 'r') as f:
                fixed_profile_data = json.load(f)
            
            print(f"\nAfter fix:")
            print(f"  Profile owned_cars: {fixed_profile_data.get('inventory', {}).get('owned_cars', [])}")
            print(f"  Profile selected_car: {fixed_profile_data.get('cars', {}).get('selected_car', 'N/A')}")
            
            # Verify fix
            fixed_owned_cars = fixed_profile_data.get("inventory", {}).get("owned_cars", [])
            fixed_selected_car = fixed_profile_data.get("cars", {}).get("selected_car", 0)
            
            if len(fixed_owned_cars) == len(garage_data):
                print("  ✅ Owned cars count now matches garage entries")
            else:
                print("  ❌ Owned cars count still doesn't match garage entries")
                return False
            
            if fixed_selected_car < len(garage_data):
                print("  ✅ Selected car index is now valid")
            else:
                print("  ❌ Selected car index is still invalid")
                return False
            
            return True
        else:
            print("  ❌ Fix failed or no fix was needed")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

def test_garage_access():
    """Test that garage can be accessed after fix"""
    print("\n=== Testing Garage Access ===")
    
    try:
        # Try to simulate garage screen initialization
        with open('data/garage.json') as f:
            cars_data = json.load(f)
        with open('data/profile.json') as f:
            profile_data = json.load(f)
        
        selected_car_index = profile_data["cars"].get("selected_car", -1)
        owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
        
        print(f"Selected car index: {selected_car_index}")
        print(f"Owned cars: {owned_cars}")
        print(f"Garage entries: {len(cars_data)}")
        
        if not owned_cars or selected_car_index < 0 or selected_car_index >= len(owned_cars):
            print("  ⚠️  No valid car selected - garage will show 'no car' message")
            return True
        
        if selected_car_index >= len(cars_data):
            print("  ❌ Selected car index exceeds garage data")
            return False
        
        selected_car = cars_data[selected_car_index]
        print(f"  ✅ Successfully accessed car: {selected_car.get('name', 'Unknown')}")
        return True
        
    except Exception as e:
        print(f"❌ Garage access test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 Car Index Management Fix - Test Suite")
    print("=" * 50)
    
    tests = [
        test_car_index_fix,
        test_garage_access
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🔧 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Car index management is working correctly!")
        print("You should now be able to access the garage without issues.")
        return True
    else:
        print("⚠️  Some issues remain - check the output above")
        return False

if __name__ == "__main__":
    main()
