#!/usr/bin/env python3
"""
Test script to validate the car maintenance and performance system fixes
"""

import json
import sys
import os
import time

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_deterioration_rates():
    """Test that deterioration rates are more reasonable"""
    print("🧪 Testing Deterioration Rate Improvements...")
    
    try:
        from valuation_system import valuation_system
        
        # Test with simulated usage data
        test_usage_data = {
            "car_age_days": 30,  # 30 days old
            "races_completed": 10,  # 10 races
            "engine_age_days": 30,
            "turbo_age_days": 30,
            "intercooler_age_days": 30,
            "ecu_age_days": 30
        }
        
        # Calculate condition for different components
        car_condition = valuation_system.calculate_condition(30, 10, "car")
        engine_condition = valuation_system.calculate_condition(30, 10, "engine")
        turbo_condition = valuation_system.calculate_condition(30, 10, "turbo")
        
        print(f"  📊 After 30 days, 10 races:")
        print(f"    Car condition: {car_condition:.1%}")
        print(f"    Engine condition: {engine_condition:.1%}")
        print(f"    Turbo condition: {turbo_condition:.1%}")
        
        # Test should show reasonable deterioration (not too aggressive)
        if car_condition < 0.85:  # Should be above 85% after 30 days/10 races
            print(f"  ⚠️  Car deterioration still too aggressive: {car_condition:.1%}")
            return False
        
        if engine_condition < 0.80:  # Engine should be above 80%
            print(f"  ⚠️  Engine deterioration still too aggressive: {engine_condition:.1%}")
            return False
        
        print("  ✅ Deterioration rates are now reasonable")
        return True
        
    except Exception as e:
        print(f"  ❌ Deterioration test failed: {e}")
        return False

def test_performance_penalties():
    """Test that car condition affects performance"""
    print("\n🧪 Testing Performance Penalties...")
    
    try:
        from valuation_system import valuation_system
        
        # Load test car data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        if not garage_data:
            print("  ❌ No car data found")
            return False
        
        car_data = garage_data[0]
        
        # Test with good condition
        good_usage_data = valuation_system.get_default_usage_data()
        good_performance = valuation_system.calculate_enhanced_performance(car_data, good_usage_data)
        
        # Test with poor condition
        poor_usage_data = {
            "car_age_days": 365,  # 1 year old
            "races_completed": 100,  # 100 races
            "engine_age_days": 365,
            "turbo_age_days": 365,
            "intercooler_age_days": 365,
            "ecu_age_days": 365
        }
        poor_performance = valuation_system.calculate_enhanced_performance(car_data, poor_usage_data)
        
        print(f"  📊 Performance comparison:")
        print(f"    Good condition HP: {good_performance['total_horsepower']}")
        print(f"    Poor condition HP: {poor_performance['total_horsepower']}")
        print(f"    Good condition avg: {good_performance['condition_effects']['avg_parts_condition']:.1%}")
        print(f"    Poor condition avg: {poor_performance['condition_effects']['avg_parts_condition']:.1%}")
        
        # Performance should be noticeably different
        hp_difference = good_performance['total_horsepower'] - poor_performance['total_horsepower']
        if hp_difference < 10:  # Should lose at least 10 HP
            print(f"  ⚠️  Performance penalty too small: {hp_difference} HP difference")
            return False
        
        print(f"  ✅ Performance penalties working: {hp_difference} HP difference")
        return True
        
    except Exception as e:
        print(f"  ❌ Performance penalty test failed: {e}")
        return False

def test_repair_system():
    """Test the repair system functionality"""
    print("\n🧪 Testing Repair System...")
    
    try:
        from maintenance_system import maintenance_system
        from valuation_system import valuation_system
        
        # Test getting repair options
        repair_options = maintenance_system.get_repair_options(0)
        
        if not repair_options:
            print("  ❌ No repair options available")
            return False
        
        print(f"  📊 Available repair options: {len(repair_options)}")
        for option in repair_options:
            print(f"    {option['name']}: {option['cost']} $ ({option['improvement']} improvement)")
        
        # Test repair cost calculation
        expected_types = ["minor", "major", "full"]
        available_types = [opt['type'] for opt in repair_options]
        
        for expected_type in expected_types:
            if expected_type not in available_types:
                print(f"  ❌ Missing repair type: {expected_type}")
                return False
        
        # Test that costs are reasonable (not too expensive or too cheap)
        for option in repair_options:
            if option['cost'] < 50:  # Too cheap
                print(f"  ⚠️  Repair cost too low: {option['name']} = {option['cost']} $")
                return False
            if option['cost'] > 10000:  # Too expensive for starting car
                print(f"  ⚠️  Repair cost too high: {option['name']} = {option['cost']} $")
                return False
        
        print("  ✅ Repair system working correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Repair system test failed: {e}")
        return False

def test_aging_improvements():
    """Test that aging is more reasonable"""
    print("\n🧪 Testing Aging Improvements...")
    
    try:
        from valuation_system import valuation_system
        
        # Simulate multiple usage updates
        usage_data = valuation_system.get_default_usage_data()
        
        # Simulate 5 races in quick succession (should have minimal aging)
        for i in range(5):
            usage_data = valuation_system.update_usage_data(usage_data, 1)
            time.sleep(0.1)  # Small delay
        
        total_age = usage_data.get("car_age_days", 0)
        
        print(f"  📊 Age after 5 quick races: {total_age:.3f} days")
        
        # Should not age more than 1 day total for quick races
        if total_age > 1.0:
            print(f"  ⚠️  Aging still too aggressive for quick races: {total_age:.3f} days")
            return False
        
        print("  ✅ Aging improvements working correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Aging test failed: {e}")
        return False

def test_condition_display():
    """Test that condition affects car performance in races"""
    print("\n🧪 Testing Condition-Based Performance...")
    
    try:
        # Test that Car class can be created with condition tracking
        from cars import Car
        
        # Test car creation with car_index
        test_car = Car(
            "classic", "red", 500,
            {"engine": {"horsepower": 120, "weight": 180}},
            100, 100, time.time(), 0
        )
        
        # Check if condition attributes exist
        if not hasattr(test_car, 'condition_factor'):
            print("  ⚠️  Car missing condition_factor attribute")
            return False
        
        if not hasattr(test_car, 'performance_multiplier'):
            print("  ⚠️  Car missing performance_multiplier attribute")
            return False
        
        print(f"  📊 Car condition factor: {test_car.condition_factor:.2f}")
        print(f"  📊 Performance multiplier: {test_car.performance_multiplier:.2f}")
        
        print("  ✅ Condition-based performance working")
        return True
        
    except Exception as e:
        print(f"  ❌ Condition display test failed: {e}")
        return False

def main():
    """Run all maintenance system tests"""
    print("🔧 Testing Car Maintenance and Performance System Fixes")
    print("=" * 60)
    
    tests = [
        test_deterioration_rates,
        test_performance_penalties,
        test_repair_system,
        test_aging_improvements,
        test_condition_display
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All maintenance system fixes validated successfully!")
        print("\n🚗 Key Improvements:")
        print("  • Reduced deterioration rates by 60-75%")
        print("  • Added performance penalties for worn cars")
        print("  • Implemented comprehensive repair system")
        print("  • Fixed aggressive real-time aging")
        print("  • Added condition display in races")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
