# 💰 System Dynamicznej Wyceny - Xtreme Cars

## 📋 Przegląd

Zaawansowany system wyceny samochodów i części z uwzględnieniem zużycia, wieku i wydajności. System oferuje realistyczną deprecjację wartości oraz możliwość sprzedaży pojazdów.

## 🎯 **Nowe Funkcje**

### ✅ **Dynamiczna Wycena Samochodów**
- **Wartość bazowa** - cena samochodu + wartość wszystkich części
- **Deprecjacja czasowa** - 0.1-0.2% wartości dziennie
- **Deprecjacja użytkowa** - 0.1-0.25% wartości za wyścig
- **Bonus wydajności** - 0.8x-1.5x mnożnik na podstawie stosunku mocy do wagi
- **<PERSON><PERSON><PERSON> stanu** - od "excellent" do "broken"

### ✅ **System Sprzedaży Pojazdów**
- **Realistyczne ceny** - 60-75% wartości rynkowej
- **Dialog sprzedaży** - szczegółowe informacje przed transakcją
- **Zabezpieczenia** - nie można sprzedać ostatniego samochodu
- **Automatyczne zapisywanie** - integracja z systemem zapisów

### ✅ **Wyświetlanie Wartości**
- **Profil gracza** - aktualna wartość wybranego samochodu
- **Garaż** - wartość każdej części z uwzględnieniem zużycia
- **Ekran sprzedaży** - kompletna analiza wartości

### ✅ **Śledzenie Zużycia**
- **Wiek części** - liczba dni od zakupu/instalacji
- **Liczba wyścigów** - automatyczne aktualizowanie po każdym wyścigu
- **Dane użytkowania** - przechowywane w profilu gracza

## 🔧 **Mechanika Wyceny**

### **Formuła Podstawowa**
```
Wartość Całkowita = (Wartość Samochodu + Wartość Części) × Bonus Wydajności
```

### **Deprecjacja Czasowa**
- **Samochód**: 0.2% dziennie
- **Silnik**: 0.1% dziennie  
- **Turbo**: 0.15% dziennie
- **Intercooler**: 0.08% dziennie
- **ECU**: 0.05% dziennie (oprogramowanie deprecjuje wolniej)

### **Deprecjacja Użytkowa (za wyścig)**
- **Samochód**: 0.1%
- **Silnik**: 0.2%
- **Turbo**: 0.25%
- **Intercooler**: 0.1%
- **ECU**: 0.02%

### **Bonus Wydajności**
| Stosunek P/W | Kategoria | Mnożnik |
|--------------|-----------|---------|
| < 0.3 | Niska wydajność | 0.8x |
| 0.3-0.8 | Średnia wydajność | 1.0x |
| 0.8-1.5 | Wysoka wydajność | 1.2x |
| > 1.5 | Ekstremalna wydajność | 1.5x |

### **Kategorie Stanu**
| Stan | Zakres | Opis |
|------|--------|------|
| Excellent | 90-100% | Jak nowy |
| Good | 70-89% | Dobry stan |
| Fair | 50-69% | Przeciętny stan |
| Poor | 30-49% | Zły stan |
| Broken | < 30% | Wymagana naprawa |

## 💸 **System Sprzedaży**

### **Ceny Sprzedaży**
- **Excellent (90%+)**: 75% wartości rynkowej
- **Good/Fair (50-89%)**: 70% wartości rynkowej  
- **Poor/Broken (<50%)**: 60% wartości rynkowej

### **Proces Sprzedaży**
1. **Wybór samochodu** w ekranie sprzedaży
2. **Dialog potwierdzenia** z pełną analizą wartości
3. **Walidacja** - sprawdzenie czy nie jest to ostatni samochód
4. **Transakcja** - dodanie pieniędzy i usunięcie samochodu
5. **Aktualizacja danych** - reindeksowanie pozostałych pojazdów

### **Zabezpieczenia**
- ✅ **Nie można sprzedać ostatniego samochodu**
- ✅ **Potwierdzenie przed sprzedażą**
- ✅ **Automatyczne zapisywanie zmian**
- ✅ **Reindeksowanie danych po sprzedaży**

## 📊 **Przykłady Wyceny**

### **Nowy Samochód (Classic)**
```
Wartość bazowa samochodu: 400 $
Części:
  - V6 Naturally Aspirated: 1200 $
  - Variable Geometry Turbo: 2800 $
  - Basic Front Mount: 300 $
  - Stage 1 Tune: 400 $

Suma części: 4700 $
Bonus wydajności: 1.0x (średnia wydajność)
Wartość całkowita: 5100 $
Cena sprzedaży: 3825 $ (75%)
```

### **Po 30 Dniach**
```
Deprecjacja czasowa: ~4%
Wartość całkowita: 4900 $
Cena sprzedaży: 3675 $ (75%)
```

### **Po 50 Wyścigach**
```
Deprecjacja użytkowa: ~10%
Wartość całkowita: 4591 $
Cena sprzedaży: 3443 $ (75%)
```

### **Po 60 Dniach + 100 Wyścigów**
```
Łączna deprecjacja: ~28%
Wartość całkowita: 3683 $
Stan: 78% (Good)
Cena sprzedaży: 2578 $ (70%)
```

## 🛠 **Implementacja Techniczna**

### **Nowe Pliki**
- **`valuation_system.py`** - główny system wyceny
- **`selling_system.py`** - system sprzedaży pojazdów
- **`VALUATION_README.md`** - dokumentacja systemu

### **Zaktualizowane Pliki**
- **`data/profile.json`** - dodane dane o zużyciu (`usage_data`)
- **`data/shop_data.json`** - dodane kategorie części
- **`data/garage.json`** - dodane kategorie części
- **`game.py`** - aktualizacja zużycia po wyścigach
- **`ui.py`** - wyświetlanie wartości w profilu
- **`garage_screen.py`** - wyświetlanie wartości części
- **`save_system.py`** - obsługa danych o zużyciu

### **Struktura Danych Zużycia**
```json
{
  "usage_data": {
    "cars": {
      "0": {
        "car_age_days": 30,
        "races_completed": 50,
        "engine_age_days": 30,
        "turbo_age_days": 30,
        "intercooler_age_days": 30,
        "ecu_age_days": 30,
        "last_update": 1703001600
      }
    }
  }
}
```

### **API Systemu Wyceny**
```python
# Oblicz wartość samochodu
valuation = valuation_system.calculate_car_value(car_data, usage_data)

# Oszacuj cenę sprzedaży
selling_info = valuation_system.estimate_selling_price(car_data, usage_data)

# Aktualizuj dane zużycia
updated_usage = valuation_system.update_usage_data(usage_data, races_increment=1)

# Oblicz wartość części
part_value = valuation_system.calculate_part_value(part_data, part_type, age_days, races)
```

## 🎮 **Doświadczenie Gracza**

### **Profil Gracza**
- **Wartość samochodu** wyświetlana z procentem stanu
- **Przykład**: "Wartość: 4900 $ (94%)"

### **Garaż**
- **Wartość każdej części** z uwzględnieniem zużycia
- **Kategoria stanu** (excellent, good, fair, poor, broken)
- **Przykład**: "Wartość: 2520 $ (90%)"

### **Ekran Sprzedaży**
- **Lista wszystkich samochodów** z wartościami
- **Ceny sprzedaży** wyróżnione zielonym kolorem
- **Dialog potwierdzenia** z pełną analizą
- **Komunikaty** o powodzeniu/błędzie transakcji

### **Po Wyścigach**
- **Automatyczna aktualizacja** danych zużycia
- **Stopniowa deprecjacja** wartości pojazdów
- **Realistyczne starzenie się** części

## 📈 **Strategiczne Implikacje**

### **Dla Gracza**
- ✅ **Planowanie zakupów** - nowe części vs używane
- ✅ **Timing sprzedaży** - sprzedaż przed nadmiernym zużyciem
- ✅ **Zarządzanie flotą** - rotacja samochodów
- ✅ **Inwestycje długoterminowe** - części wysokiej jakości

### **Dla Rozgrywki**
- ✅ **Realizm ekonomiczny** - prawdziwe koszty posiadania
- ✅ **Decyzje strategiczne** - kiedy sprzedać, kiedy kupić
- ✅ **Progresja długoterminowa** - budowanie wartości
- ✅ **Zarządzanie zasobami** - optymalizacja wydatków

## 🔮 **Przyszłe Rozszerzenia**

### **Planowane Funkcje**
- **System napraw** - przywracanie stanu części
- **Ubezpieczenia** - ochrona przed deprecjacją
- **Rynek wtórny** - kupno używanych części od innych graczy
- **Certyfikaty stanu** - oficjalne potwierdzenie kondycji

### **Możliwe Ulepszenia**
- **Sezonowa deprecjacja** - różne stawki w zależności od pory roku
- **Modyfikatory środowiskowe** - wpływ warunków na zużycie
- **Historia serwisowa** - śledzenie napraw i modyfikacji
- **Wartość kolekcjonerska** - bonus za rzadkie samochody

## 🎉 **Podsumowanie**

System dynamicznej wyceny znacznie wzbogaca rozgrywkę poprzez:

✅ **Realistyczną ekonomię** - prawdziwe koszty posiadania pojazdów  
✅ **Strategiczne decyzje** - planowanie zakupów i sprzedaży  
✅ **Długoterminową progresję** - budowanie i zarządzanie wartością  
✅ **Immersyjne doświadczenie** - samochody starzą się jak w rzeczywistości  
✅ **Dodatkową głębię** - nowy wymiar zarządzania zasobami  
✅ **Sprawiedliwy system** - zrównoważone ceny i deprecjacja  

System jest w pełni zintegrowany z grą i oferuje nowy poziom strategicznego myślenia! 💰🏁
