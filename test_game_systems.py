#!/usr/bin/env python3
"""
Test suite for game systems verification (fuel, tires, maintenance)
"""

import sys
import os
import json
import tempfile
import shutil
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fuel_system import fuel_system
from tire_system import tire_system
from maintenance_system import maintenance_system

def setup_test_profile():
    """Create a temporary test profile"""
    test_profile = {
        "username": "Test Player",
        "money": 10000,
        "level": {"current": 1, "exp": 0, "required_to_next_level": 274},
        "cars": {"selected_car": 0},
        "inventory": {"owned_cars": ["test"]},
        "fuel_data": {"cars": {}},
        "tire_data": {"cars": {}},
        "maintenance_data": {"cars": {}, "insurance": None},
        "usage_data": {"cars": {}}
    }
    
    # Save to temporary file
    from utils import resource_path
    with open(resource_path('data/profile.json'), 'w') as f:
        json.dump(test_profile, f, indent=4)
    
    return test_profile

def test_fuel_system():
    """Test fuel system functionality"""
    print("=== Testing Fuel System ===")
    
    # Setup test data
    setup_test_profile()
    
    # Test initialization
    result = fuel_system.initialize_fuel_data(0)
    if not result:
        print("❌ ERROR: Fuel system initialization failed")
        return False
    
    # Test getting fuel data
    fuel_data = fuel_system.get_fuel_data(0)
    if not fuel_data:
        print("❌ ERROR: Could not get fuel data")
        return False
    
    print(f"✅ Fuel data initialized: {fuel_data}")
    
    # Test fuel consumption
    test_car_data = {
        "name": "test",
        "weight": 1000,
        "parts": {"engine": {"horsepower": 200}}
    }
    
    initial_fuel = fuel_data["current_fuel"]
    fuel_system.consume_fuel(0, 60, test_car_data)  # 1 minute race
    
    new_fuel_data = fuel_system.get_fuel_data(0)
    if new_fuel_data["current_fuel"] >= initial_fuel:
        print("❌ ERROR: Fuel not consumed during race")
        return False
    
    print(f"✅ Fuel consumption working: {initial_fuel} → {new_fuel_data['current_fuel']}")
    
    # Test refueling
    success, message = fuel_system.refuel_car(0)
    if not success:
        print(f"❌ ERROR: Refueling failed: {message}")
        return False
    
    print(f"✅ Refueling successful: {message}")
    
    return True

def test_tire_system():
    """Test tire system functionality"""
    print("\n=== Testing Tire System ===")
    
    # Test initialization
    result = tire_system.initialize_tire_data(0)
    if not result:
        print("❌ ERROR: Tire system initialization failed")
        return False
    
    # Test getting tire data
    tire_data = tire_system.get_tire_data(0)
    if not tire_data:
        print("❌ ERROR: Could not get tire data")
        return False
    
    print(f"✅ Tire data initialized: {tire_data}")
    
    # Test tire wear
    test_car_data = {
        "name": "test",
        "weight": 1000,
        "parts": {"engine": {"horsepower": 200}}
    }
    
    initial_condition = tire_data["condition"]
    wear_result = tire_system.apply_tire_wear(0, 60, test_car_data, "aggressive_driving")
    
    if not wear_result or wear_result["new_condition"] >= initial_condition:
        print("❌ ERROR: Tire wear not applied correctly")
        return False
    
    print(f"✅ Tire wear working: {initial_condition} → {wear_result['new_condition']}")
    
    # Test tire replacement
    success, message = tire_system.replace_tires(0, "performance")
    if not success:
        print(f"❌ ERROR: Tire replacement failed: {message}")
        return False
    
    print(f"✅ Tire replacement successful: {message}")
    
    return True

def test_maintenance_system():
    """Test maintenance system functionality"""
    print("\n=== Testing Maintenance System ===")
    
    # Test initialization
    result = maintenance_system.initialize_maintenance_data(0)
    if not result:
        print("❌ ERROR: Maintenance system initialization failed")
        return False
    
    # Test maintenance due check
    is_due = maintenance_system.is_maintenance_due(0)
    print(f"✅ Maintenance due check: {is_due}")
    
    # Test maintenance cost calculation
    test_car_data = {
        "name": "test",
        "weight": 1000,
        "parts": {"engine": {"horsepower": 200}}
    }
    
    cost_info = maintenance_system.calculate_maintenance_cost(test_car_data)
    if not cost_info or cost_info["total_cost"] <= 0:
        print("❌ ERROR: Invalid maintenance cost calculation")
        return False
    
    print(f"✅ Maintenance cost calculation: {cost_info['total_cost']}$")
    
    # Test repair options
    repair_options = maintenance_system.get_repair_options(0)
    if not repair_options:
        print("❌ ERROR: No repair options available")
        return False
    
    print(f"✅ Repair options available: {len(repair_options)} options")
    
    return True

def test_system_integration():
    """Test integration between systems"""
    print("\n=== Testing System Integration ===")
    
    # Test that all systems can work with the same car
    car_index = 0
    
    # Initialize all systems
    fuel_init = fuel_system.initialize_fuel_data(car_index)
    tire_init = tire_system.initialize_tire_data(car_index)
    maint_init = maintenance_system.initialize_maintenance_data(car_index)
    
    if not all([fuel_init, tire_init, maint_init]):
        print("❌ ERROR: System initialization failed")
        return False
    
    # Test that systems don't interfere with each other
    fuel_data = fuel_system.get_fuel_data(car_index)
    tire_data = tire_system.get_tire_data(car_index)
    
    if not fuel_data or not tire_data:
        print("❌ ERROR: Systems interfering with each other")
        return False
    
    print("✅ System integration working correctly")
    
    return True

def test_error_handling():
    """Test error handling in systems"""
    print("\n=== Testing Error Handling ===")
    
    # Test invalid car index
    invalid_index = 999
    
    fuel_data = fuel_system.get_fuel_data(invalid_index)
    tire_data = tire_system.get_tire_data(invalid_index)
    
    # Should return default data, not crash
    if not fuel_data or not tire_data:
        print("❌ ERROR: Systems not handling invalid indices gracefully")
        return False
    
    print("✅ Error handling working correctly")
    
    return True

def main():
    """Run all game systems tests"""
    print("Game Systems Verification Test Suite")
    print("=" * 60)
    
    # Backup original profile if exists
    backup_path = None
    if os.path.exists('data/profile.json'):
        backup_path = 'data/profile.json.backup'
        shutil.copy('data/profile.json', backup_path)
    
    try:
        tests = [
            test_fuel_system,
            test_tire_system,
            test_maintenance_system,
            test_system_integration,
            test_error_handling
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    print(f"❌ Test {test.__name__} FAILED")
            except Exception as e:
                print(f"❌ Test {test.__name__} CRASHED: {e}")
        
        print("\n" + "=" * 60)
        print(f"🔧 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("ALL GAME SYSTEMS TESTS PASSED!")
            print("Fuel system: WORKING")
            print("Tire system: WORKING")
            print("Maintenance system: WORKING")
            print("System integration: WORKING")
            print("Error handling: WORKING")
        else:
            print("❌ SOME TESTS FAILED - Game systems need fixes")
        
        return passed == total
        
    finally:
        # Restore backup if it exists
        if backup_path and os.path.exists(backup_path):
            shutil.copy(backup_path, 'data/profile.json')
            os.remove(backup_path)

if __name__ == '__main__':
    main()
